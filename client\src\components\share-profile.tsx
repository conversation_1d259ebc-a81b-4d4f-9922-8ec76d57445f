import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { 
  Share2, 
  Copy, 
  Mail, 
  MessageCircle, 
  QrCode,
  X,
  Download
} from "lucide-react";
import QRCode from "qrcode";

interface ShareProfileProps {
  username: string;
  onClose: () => void;
}

export default function ShareProfile({ username, onClose }: ShareProfileProps) {
  const { toast } = useToast();
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string | null>(null);
  const [isGeneratingQR, setIsGeneratingQR] = useState(false);

  const profileUrl = `${window.location.origin}/profile/${username}`;

  const { data: user } = useQuery({
    queryKey: ["/api/auth/user"],
    staleTime: 1000 * 60 * 10,
  });

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: "Profile link copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const generateQRCode = async () => {
    setIsGeneratingQR(true);
    try {
      const qrDataUrl = await QRCode.toDataURL(profileUrl, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
      });
      setQrCodeDataUrl(qrDataUrl);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate QR code",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingQR(false);
    }
  };

  const downloadQRCode = () => {
    if (!qrCodeDataUrl) return;
    
    const link = document.createElement('a');
    link.download = `${username}-profile-qr.png`;
    link.href = qrCodeDataUrl;
    link.click();
  };

  const shareViaEmail = () => {
    const subject = encodeURIComponent(`Check out ${user?.firstName || username}'s professional profile`);
    const body = encodeURIComponent(`I'd like to share my professional profile with you: ${profileUrl}`);
    window.open(`mailto:?subject=${subject}&body=${body}`);
  };

  const shareViaWhatsApp = () => {
    const message = encodeURIComponent(`Check out my professional profile: ${profileUrl}`);
    window.open(`https://wa.me/?text=${message}`);
  };

  const shareViaMessenger = () => {
    const message = encodeURIComponent(profileUrl);
    window.open(`fb-messenger://share?text=${message}`);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Share2 className="w-5 h-5" />
              Share Profile
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Profile URL */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Profile URL</label>
            <div className="flex gap-2">
              <Input 
                value={profileUrl} 
                readOnly
                className="flex-1"
              />
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => copyToClipboard(profileUrl)}
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Social Share Buttons */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Share via</label>
            <div className="grid grid-cols-1 gap-2">
              <Button 
                variant="outline" 
                className="justify-start"
                onClick={shareViaEmail}
              >
                <Mail className="w-4 h-4 mr-2" />
                Email
              </Button>
              <Button 
                variant="outline" 
                className="justify-start"
                onClick={shareViaWhatsApp}
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                WhatsApp
              </Button>
              <Button 
                variant="outline" 
                className="justify-start"
                onClick={shareViaMessenger}
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Messenger
              </Button>
            </div>
          </div>

          {/* QR Code Section */}
          <div className="space-y-3">
            <label className="text-sm font-medium">QR Code</label>
            
            {!qrCodeDataUrl ? (
              <Button 
                variant="outline" 
                className="w-full justify-center"
                onClick={generateQRCode}
                disabled={isGeneratingQR}
              >
                <QrCode className="w-4 h-4 mr-2" />
                {isGeneratingQR ? "Generating..." : "Generate QR Code"}
              </Button>
            ) : (
              <div className="space-y-3">
                <div className="flex justify-center">
                  <img 
                    src={qrCodeDataUrl} 
                    alt="Profile QR Code"
                    className="border rounded-lg"
                  />
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    className="flex-1"
                    onClick={downloadQRCode}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setQrCodeDataUrl(null)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Close Button */}
          <Button 
            variant="outline" 
            className="w-full"
            onClick={onClose}
          >
            Close
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}