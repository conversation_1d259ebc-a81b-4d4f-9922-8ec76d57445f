import {
  pgTable,
  text,
  varchar,
  timestamp,
  jsonb,
  index,
  serial,
  integer,
  boolean,
} from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

// Session storage table (required for Replit Auth)
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// Users table (required for Replit Auth)
export const users = pgTable("users", {
  id: varchar("id").primaryKey().notNull(),
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  username: varchar("username").unique(),
  plan: varchar("plan", { enum: ["free", "pro", "agency"] }).default("free"),
  userType: varchar("user_type", { enum: ["user", "admin"] }).default("user"),
  subscriptionExpiresAt: timestamp("subscription_expires_at"),
  mayaCustomerId: varchar("maya_customer_id"),
  mayaCheckoutId: varchar("maya_checkout_id"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Resumes table
export const resumes = pgTable("resumes", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  title: varchar("title"),
  summary: text("summary"),
  phone: varchar("phone"),
  location: varchar("location"),
  website: varchar("website"),
  profilePhotoUrl: text("profile_photo_url"),
  skills: text("skills").array(),
  experience: jsonb("experience").$type<Array<{
    title: string;
    company: string;
    duration: string;
    description: string;
  }>>(),
  education: jsonb("education").$type<Array<{
    degree: string;
    school: string;
    year: string;
    description?: string;
  }>>(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Credentials table
export const credentials = pgTable("credentials", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  title: varchar("title").notNull(),
  description: text("description"),
  fileUrl: text("file_url").notNull(),
  fileName: varchar("file_name").notNull(),
  fileSize: integer("file_size"),
  fileType: varchar("file_type"),
  isPublic: boolean("is_public").default(false),
  issuer: varchar("issuer"),
  issuedDate: varchar("issued_date"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ one, many }) => ({
  resume: one(resumes),
  credentials: many(credentials),
  profileViews: many(profileViews),
  downloadEvents: many(downloadEvents),
  credentialUploadEvents: many(credentialUploadEvents),
}));

export const resumesRelations = relations(resumes, ({ one }) => ({
  user: one(users, { fields: [resumes.userId], references: [users.id] }),
}));

export const credentialsRelations = relations(credentials, ({ one }) => ({
  user: one(users, { fields: [credentials.userId], references: [users.id] }),
}));

// Analytics tables
export const profileViews = pgTable("profile_views", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id),
  viewerIp: varchar("viewer_ip"),
  viewerCountry: varchar("viewer_country"),
  viewerCity: varchar("viewer_city"),
  userAgent: text("user_agent"),
  referrer: text("referrer"),
  viewedAt: timestamp("viewed_at").defaultNow().notNull(),
});

export const downloadEvents = pgTable("download_events", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id),
  downloadType: varchar("download_type").notNull(), // 'resume_pdf', 'credential'
  resourceId: varchar("resource_id"), // credential ID if downloading credential
  downloaderIp: varchar("downloader_ip"),
  downloaderCountry: varchar("downloader_country"),
  downloaderCity: varchar("downloader_city"),
  userAgent: text("user_agent"),
  downloadedAt: timestamp("downloaded_at").defaultNow().notNull(),
});

export const credentialUploadEvents = pgTable("credential_upload_events", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id),
  credentialId: integer("credential_id").notNull().references(() => credentials.id),
  uploadedAt: timestamp("uploaded_at").defaultNow().notNull(),
});

// Analytics relations
export const profileViewsRelations = relations(profileViews, ({ one }) => ({
  user: one(users, {
    fields: [profileViews.userId],
    references: [users.id],
  }),
}));

export const downloadEventsRelations = relations(downloadEvents, ({ one }) => ({
  user: one(users, {
    fields: [downloadEvents.userId],
    references: [users.id],
  }),
}));

export const credentialUploadEventsRelations = relations(credentialUploadEvents, ({ one }) => ({
  user: one(users, {
    fields: [credentialUploadEvents.userId],
    references: [users.id],
  }),
  credential: one(credentials, {
    fields: [credentialUploadEvents.credentialId],
    references: [credentials.id],
  }),
}));

// Insert schemas
export const insertUserSchema = createInsertSchema(users).omit({
  createdAt: true,
  updatedAt: true,
});

export const insertResumeSchema = createInsertSchema(resumes).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertCredentialSchema = createInsertSchema(credentials).omit({
  id: true,
  createdAt: true,
});

// Analytics insert schemas
export const insertProfileViewSchema = createInsertSchema(profileViews).omit({
  id: true,
  viewedAt: true,
});

export const insertDownloadEventSchema = createInsertSchema(downloadEvents).omit({
  id: true,
  downloadedAt: true,
});

export const insertCredentialUploadEventSchema = createInsertSchema(credentialUploadEvents).omit({
  id: true,
  uploadedAt: true,
});

// Types
export type UpsertUser = typeof users.$inferInsert;
export type User = typeof users.$inferSelect;
export type InsertResume = z.infer<typeof insertResumeSchema>;
export type Resume = typeof resumes.$inferSelect;
export type InsertCredential = z.infer<typeof insertCredentialSchema>;
export type Credential = typeof credentials.$inferSelect;

// Analytics types
export type InsertProfileView = z.infer<typeof insertProfileViewSchema>;
export type ProfileView = typeof profileViews.$inferSelect;
export type InsertDownloadEvent = z.infer<typeof insertDownloadEventSchema>;
export type DownloadEvent = typeof downloadEvents.$inferSelect;
export type InsertCredentialUploadEvent = z.infer<typeof insertCredentialUploadEventSchema>;
export type CredentialUploadEvent = typeof credentialUploadEvents.$inferSelect;

// Payment tracking table
export const payments = pgTable("payments", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  mayaCheckoutId: varchar("maya_checkout_id").notNull().unique(),
  referenceNumber: varchar("reference_number").notNull(),
  amount: integer("amount").notNull(), // in centavos
  currency: varchar("currency", { length: 3 }).default("PHP"),
  status: varchar("status", { enum: ["pending", "paid", "failed", "cancelled", "expired"] }).default("pending"),
  paymentMethod: varchar("payment_method"),
  receiptNumber: varchar("receipt_number"),
  paidAt: timestamp("paid_at"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Payment relations
export const paymentsRelations = relations(payments, ({ one }) => ({
  user: one(users, { fields: [payments.userId], references: [users.id] }),
}));

// Payment insert schema
export const insertPaymentSchema = createInsertSchema(payments).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Payment types
export type InsertPayment = z.infer<typeof insertPaymentSchema>;
export type Payment = typeof payments.$inferSelect;

// Manual billing update logs
export const manualBillingLogs = pgTable("manual_billing_logs", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  oldStatus: varchar("old_status").notNull(),
  newStatus: varchar("new_status").notNull(),
  oldExpiryDate: timestamp("old_expiry_date"),
  newExpiryDate: timestamp("new_expiry_date"),
  transactionReference: varchar("transaction_reference"),
  reason: text("reason").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

// Transaction cleanup logs
export const transactionCleanupLogs = pgTable("transaction_cleanup_logs", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  removedTransactions: integer("removed_transactions").notNull(),
  cleanupReason: text("cleanup_reason").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

// Manual billing relations
export const manualBillingLogsRelations = relations(manualBillingLogs, ({ one }) => ({
  user: one(users, { fields: [manualBillingLogs.userId], references: [users.id] }),
}));

export const transactionCleanupLogsRelations = relations(transactionCleanupLogs, ({ one }) => ({
  user: one(users, { fields: [transactionCleanupLogs.userId], references: [users.id] }),
}));

// Manual billing insert schemas
export const insertManualBillingLogSchema = createInsertSchema(manualBillingLogs).omit({
  id: true,
  createdAt: true,
});

export const insertTransactionCleanupLogSchema = createInsertSchema(transactionCleanupLogs).omit({
  id: true,
  createdAt: true,
});

// Manual billing types
export type InsertManualBillingLog = z.infer<typeof insertManualBillingLogSchema>;
export type ManualBillingLog = typeof manualBillingLogs.$inferSelect;
export type InsertTransactionCleanupLog = z.infer<typeof insertTransactionCleanupLogSchema>;
export type TransactionCleanupLog = typeof transactionCleanupLogs.$inferSelect;
