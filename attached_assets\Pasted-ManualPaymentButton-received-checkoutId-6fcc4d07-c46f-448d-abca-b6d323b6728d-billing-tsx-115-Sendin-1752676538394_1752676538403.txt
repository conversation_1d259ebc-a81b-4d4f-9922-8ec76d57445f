ManualPaymentButton received checkoutId: 6fcc4d07-c46f-448d-abca-b6d323b6728d
billing.tsx:115 Sending payment completion request with checkoutId: 6fcc4d07-c46f-448d-abca-b6d323b6728d
billing.tsx:111 ManualPaymentButton received checkoutId: 6fcc4d07-c46f-448d-abca-b6d323b6728d
queryClient.ts:19 
            
            
           POST https://3c95dd65-2de7-485f-9658-26e90db7d6cc-00-30cnepm1vr00g.kirk.replit.dev/api/complete-payment 404 (Not Found)
apiRequest @ queryClient.ts:19
mutationFn @ billing.tsx:116
fn @ @tanstack_react-query.js?v=66a9ae55:1189
run @ @tanstack_react-query.js?v=66a9ae55:494
start @ @tanstack_react-query.js?v=66a9ae55:536
execute @ @tanstack_react-query.js?v=66a9ae55:1225
await in execute
mutate @ @tanstack_react-query.js?v=66a9ae55:2630
(anonymous) @ @tanstack_react-query.js?v=66a9ae55:3295
onClick @ billing.tsx:163
callCallback2 @ chunk-WERSD76P.js?v=66a9ae55:3674
invokeGuardedCallbackDev @ chunk-WERSD76P.js?v=66a9ae55:3699
invokeGuardedCallback @ chunk-WERSD76P.js?v=66a9ae55:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-WERSD76P.js?v=66a9ae55:3736
executeDispatch @ chunk-WERSD76P.js?v=66a9ae55:7014
processDispatchQueueItemsInOrder @ chunk-WERSD76P.js?v=66a9ae55:7034
processDispatchQueue @ chunk-WERSD76P.js?v=66a9ae55:7043
dispatchEventsForPlugins @ chunk-WERSD76P.js?v=66a9ae55:7051
(anonymous) @ chunk-WERSD76P.js?v=66a9ae55:7174
batchedUpdates$1 @ chunk-WERSD76P.js?v=66a9ae55:18913
batchedUpdates @ chunk-WERSD76P.js?v=66a9ae55:3579
dispatchEventForPluginEventSystem @ chunk-WERSD76P.js?v=66a9ae55:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-WERSD76P.js?v=66a9ae55:5478
dispatchEvent @ chunk-WERSD76P.js?v=66a9ae55:5472
dispatchDiscreteEvent @ chunk-WERSD76P.js?v=66a9ae55:5449Understand this error
billing.tsx:210 Payment data received: [{…}]
billing.tsx:211 Latest payment: {id: 3, mayaCheckoutId: '6fcc4d07-c46f-448d-abca-b6d323b6728d', referenceNumber: 'JUANCV-43752595-1752675732317', amount: 9900, currency: 'PHP', …}
billing.tsx:111 ManualPaymentButton received checkoutId: 6fcc4d07-c46f-448d-abca-b6d323b6728d