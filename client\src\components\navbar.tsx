import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { Link, useLocation } from "wouter";
import { Crown, User, LogOut, Menu, BarChart3, Webhook, Settings } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState } from "react";

export default function Navbar() {
  const { user, isAuthenticated } = useAuth();
  const [location] = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const userName = user?.firstName
    ? `${user.firstName} ${user.lastName || ""}`.trim()
    : user?.email || "User";

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0">
              <h1 className="text-2xl font-bold text-gray-900">
                Juan<span className="text-yellow-500">CV</span>
              </h1>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:ml-10 md:flex md:space-x-8">
              {isAuthenticated && (
                <>
                  <Link
                    href="/"
                    className={`px-3 py-2 text-sm font-medium transition-colors ${
                      location === "/"
                        ? "text-yellow-600 border-b-2 border-yellow-500"
                        : "text-gray-500 hover:text-gray-900"
                    }`}
                  >
                    Dashboard
                  </Link>
                  <Link
                    href="/billing"
                    className={`px-3 py-2 text-sm font-medium transition-colors ${
                      location === "/billing"
                        ? "text-yellow-600 border-b-2 border-yellow-500"
                        : "text-gray-500 hover:text-gray-900"
                    }`}
                  >
                    Billing
                  </Link>
                  {user?.userType === "admin" && (
                    <>
                      <Link
                        href="/admin/manual-billing"
                        className={`px-3 py-2 text-sm font-medium transition-colors ${
                          location === "/admin/manual-billing"
                            ? "text-red-600 border-b-2 border-red-500"
                            : "text-red-500 hover:text-red-700"
                        }`}
                      >
                        Admin Billing
                      </Link>
                      <Link
                        href="/webhook-monitor"
                        className={`px-3 py-2 text-sm font-medium transition-colors ${
                          location === "/webhook-monitor"
                            ? "text-red-600 border-b-2 border-red-500"
                            : "text-red-500 hover:text-red-700"
                        }`}
                      >
                        Webhook Monitor
                      </Link>
                    </>
                  )}
                  {/* <Link 
                    href="/analytics" 
                    className={`px-3 py-2 text-sm font-medium transition-colors ${
                      location === '/analytics' 
                        ? 'text-yellow-600 border-b-2 border-yellow-500' 
                        : 'text-gray-500 hover:text-gray-900'
                    }`}
                  >
                    Analytics
                  </Link> */}
                </>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                {/* Upgrade Button for Free Users */}
                {user?.plan === "free" && (
                  <Link href="/billing">
                    <Button
                      size="sm"
                      className="bg-yellow-500 hover:bg-yellow-600 hidden sm:flex"
                    >
                      <Crown className="w-4 h-4 mr-2" />
                      Upgrade
                    </Button>
                  </Link>
                )}

                {/* User Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="relative h-8 w-8 rounded-full"
                    >
                      {user?.profileImageUrl ? (
                        <img
                          src={user.profileImageUrl}
                          alt="Profile"
                          className="h-8 w-8 rounded-full object-cover"
                        />
                      ) : (
                        <div className="h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center">
                          <User className="h-4 w-4 text-yellow-600" />
                        </div>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <div className="flex flex-col space-y-1 p-2">
                      <p className="text-sm font-medium leading-none">
                        {userName}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user?.email}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground capitalize">
                        {user?.plan || "Free"} Plan
                      </p>
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/">Dashboard</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/billing">Billing</Link>
                    </DropdownMenuItem>
                    {user?.userType === "admin" && (
                      <>
                        <DropdownMenuItem asChild>
                          <Link href="/admin/manual-billing" className="text-red-600">
                            <Settings className="w-4 h-4 mr-2" />
                            Admin Billing
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/webhook-monitor" className="text-red-600">
                            <Webhook className="w-4 h-4 mr-2" />
                            Webhook Monitor
                          </Link>
                        </DropdownMenuItem>
                      </>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-red-600"
                      onClick={() => (window.location.href = "/api/logout")}
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      Log out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Mobile Menu Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="md:hidden"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="ghost"
                  onClick={() => (window.location.href = "/api/login")}
                >
                  Log In
                </Button>
                <Button
                  className="bg-yellow-500 hover:bg-yellow-600"
                  onClick={() => (window.location.href = "/api/login")}
                >
                  Get Started Free
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && isAuthenticated && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200">
              <Link
                href="/"
                className={`block px-3 py-2 text-base font-medium transition-colors ${
                  location === "/"
                    ? "text-yellow-600 bg-yellow-50"
                    : "text-gray-500 hover:text-gray-900"
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Dashboard
              </Link>
              <Link
                href="/billing"
                className={`block px-3 py-2 text-base font-medium transition-colors ${
                  location === "/billing"
                    ? "text-yellow-600 bg-yellow-50"
                    : "text-gray-500 hover:text-gray-900"
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Billing
              </Link>
              {user?.userType === "admin" && (
                <Link
                  href="/admin/manual-billing"
                  className={`block px-3 py-2 text-base font-medium transition-colors ${
                    location === "/admin/manual-billing"
                      ? "text-red-600 bg-red-50"
                      : "text-red-500 hover:text-red-700"
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Admin Billing
                </Link>
              )}
              {user?.plan === "free" && (
                <Link
                  href="/billing"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Button
                    size="sm"
                    className="w-full bg-yellow-500 hover:bg-yellow-600 mt-2"
                  >
                    <Crown className="w-4 h-4 mr-2" />
                    Upgrade to Pro
                  </Button>
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
