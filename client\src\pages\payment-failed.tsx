import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { <PERSON>Cir<PERSON>, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function PaymentFailed() {
  const [, setLocation] = useLocation();
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    // Get checkout ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const checkoutId = urlParams.get('checkout_id');
    
    console.log('Payment failed page loaded with checkout ID:', checkoutId);
    
    // Send failure message to parent window (if opened in popup)
    if (window.opener && !window.opener.closed) {
      window.opener.postMessage({
        type: 'PAYMENT_FAILED',
        checkoutId: checkoutId
      }, window.location.origin);
      
      // Close popup after sending message
      setTimeout(() => {
        window.close();
      }, 1000);
    } else {
      // If not in popup, redirect after countdown
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            setLocation('/billing');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [setLocation]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <XCircle className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-red-800">
            Payment Failed
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            We were unable to process your payment. Please try again or contact support if the issue persists.
          </p>
          
          {window.opener ? (
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Closing window...</span>
            </div>
          ) : (
            <>
              <p className="text-sm text-gray-500">
                Redirecting to billing page in {countdown} seconds...
              </p>
              <Button 
                onClick={() => setLocation('/billing')}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                Try Again
              </Button>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}