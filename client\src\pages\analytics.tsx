import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Eye, Download, Upload, Calendar, User } from "lucide-react";
import { useEffect } from "react";

interface AnalyticsSummary {
  profileViews: number;
  downloads: number;
  credentialUploads: number;
  recentViews: Array<{
    id: number;
    viewerIp: string;
    viewerCountry?: string;
    viewerCity?: string;
    userAgent?: string;
    referrer?: string;
    viewedAt: string;
  }>;
  recentDownloads: Array<{
    id: number;
    downloadType: string;
    resourceId?: string;
    downloaderIp: string;
    downloaderCountry?: string;
    downloaderCity?: string;
    userAgent?: string;
    downloadedAt: string;
  }>;
}

export default function Analytics() {
  const { user, isAuthenticated, isLoading } = useAuth();

  const { data: analytics, isLoading: analyticsLoading } = useQuery<AnalyticsSummary>({
    queryKey: ['/api/analytics/summary'],
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnMount: false,
  });

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      window.location.href = "/api/login";
    }
  }, [isAuthenticated, isLoading]);

  if (isLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Analytics Dashboard</h1>
          <p className="text-lg text-gray-600">
            Track your profile views, downloads, and engagement metrics
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="bg-white/70 backdrop-blur-sm border-amber-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Profile Views</CardTitle>
              <Eye className="h-4 w-4 text-amber-600" />
            </CardHeader>
            <CardContent>
              {analyticsLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <div className="text-2xl font-bold text-gray-900">
                  {analytics?.profileViews || 0}
                </div>
              )}
              <p className="text-xs text-gray-600">
                Total views of your public profile
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white/70 backdrop-blur-sm border-amber-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Downloads</CardTitle>
              <Download className="h-4 w-4 text-amber-600" />
            </CardHeader>
            <CardContent>
              {analyticsLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <div className="text-2xl font-bold text-gray-900">
                  {analytics?.downloads || 0}
                </div>
              )}
              <p className="text-xs text-gray-600">
                PDF downloads and credential views
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white/70 backdrop-blur-sm border-amber-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Uploads</CardTitle>
              <Upload className="h-4 w-4 text-amber-600" />
            </CardHeader>
            <CardContent>
              {analyticsLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <div className="text-2xl font-bold text-gray-900">
                  {analytics?.credentialUploads || 0}
                </div>
              )}
              <p className="text-xs text-gray-600">
                Credentials uploaded to your profile
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Views */}
          <Card className="bg-white/70 backdrop-blur-sm border-amber-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-amber-600" />
                Recent Profile Views
              </CardTitle>
              <CardDescription>
                Latest visitors to your public profile
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-full mb-2" />
                        <Skeleton className="h-3 w-2/3" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {analytics?.recentViews?.length ? (
                    analytics.recentViews.map((view) => (
                      <div key={view.id} className="flex items-start gap-3 p-3 bg-amber-50/50 rounded-lg">
                        <div className="flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-amber-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium text-gray-900">
                              {view.viewerCountry || 'Unknown Location'}
                            </span>
                            {view.viewerCity && (
                              <span className="text-xs text-gray-500">
                                • {view.viewerCity}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <Calendar className="h-3 w-3" />
                            {new Date(view.viewedAt).toLocaleDateString()} at{' '}
                            {new Date(view.viewedAt).toLocaleTimeString()}
                          </div>
                          {view.referrer && (
                            <div className="mt-1">
                              <Badge variant="secondary" className="text-xs">
                                From: {new URL(view.referrer).hostname}
                              </Badge>
                            </div>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Eye className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No profile views yet</p>
                      <p className="text-sm">Share your profile link to start tracking views!</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Downloads */}
          <Card className="bg-white/70 backdrop-blur-sm border-amber-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5 text-amber-600" />
                Recent Downloads
              </CardTitle>
              <CardDescription>
                Latest downloads of your resume and credentials
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-full mb-2" />
                        <Skeleton className="h-3 w-2/3" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {analytics?.recentDownloads?.length ? (
                    analytics.recentDownloads.map((download) => (
                      <div key={download.id} className="flex items-start gap-3 p-3 bg-amber-50/50 rounded-lg">
                        <div className="flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                          <Download className="h-4 w-4 text-amber-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium text-gray-900">
                              {download.downloadType === 'resume_pdf' ? 'Resume PDF' : 'Credential'}
                            </span>
                            <Badge variant="outline" className="text-xs">
                              {download.downloaderCountry || 'Unknown'}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <Calendar className="h-3 w-3" />
                            {new Date(download.downloadedAt).toLocaleDateString()} at{' '}
                            {new Date(download.downloadedAt).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Download className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No downloads yet</p>
                      <p className="text-sm">People will see downloads when they get your resume!</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}