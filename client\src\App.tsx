import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useAuth } from "@/hooks/useAuth";
import NotFound from "@/pages/not-found";
import Landing from "@/pages/landing";
import Dashboard from "@/pages/dashboard";
import PublicProfile from "@/pages/public-profile";
import Billing from "@/pages/billing";
import Analytics from "@/pages/analytics";

import AdminBilling from "@/pages/admin-billing";
import PaymentSuccess from "@/pages/payment-success";
import PaymentFailed from "@/pages/payment-failed";

function Router() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin w-8 h-8 border-4 border-yellow-500 border-t-transparent rounded-full" />
      </div>
    );
  }

  return (
    <Switch>
      {!isAuthenticated ? (
        <>
          <Route path="/" component={Landing} />
          <Route path="/profile/:username" component={PublicProfile} />
          <Route path="/payment/success" component={PaymentSuccess} />
          <Route path="/payment/failed" component={PaymentFailed} />
        </>
      ) : (
        <>
          <Route path="/" component={Dashboard} />
          <Route path="/billing" component={Billing} />
          <Route path="/analytics" component={Analytics} />

          <Route path="/admin/manual-billing" component={AdminBilling} />
          <Route path="/profile/:username" component={PublicProfile} />
          <Route path="/payment/success" component={PaymentSuccess} />
          <Route path="/payment/failed" component={PaymentFailed} />
        </>
      )}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
