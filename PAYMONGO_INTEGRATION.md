# PayMongo Integration Documentation

## Overview

This document provides comprehensive documentation for integrating PayMongo payment processing into JuanCV. PayMongo is a Philippine-focused payment gateway that supports multiple payment methods including credit/debit cards, GCash, GrabPay, and PayMaya.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Backend Implementation](#backend-implementation)
4. [Frontend Implementation](#frontend-implementation)
5. [Database Schema](#database-schema)
6. [Testing](#testing)
7. [Webhook Configuration](#webhook-configuration)
8. [Error Handling](#error-handling)
9. [Security Considerations](#security-considerations)
10. [Troubleshooting](#troubleshooting)

## Prerequisites

- PayMongo account (https://paymongo.com)
- Node.js application with Express.js
- PostgreSQL database
- Environment variable management

## Environment Setup

### Required Environment Variables

Add these environment variables to your Replit Secrets or `.env` file:

```env
# PayMongo API Keys
PAYMONGO_SECRET_KEY=sk_test_your_secret_key_here
PAYMONGO_PUBLIC_KEY=pk_test_your_public_key_here

# Database (if using PostgreSQL)
DATABASE_URL=your_database_url_here
```

### Getting PayMongo API Keys

1. Sign up at https://paymongo.com
2. Complete business verification
3. Navigate to Dashboard → Developers → API Keys
4. Copy your test keys for development:
   - Secret Key (starts with `sk_test_`)
   - Public Key (starts with `pk_test_`)

## Backend Implementation

### 1. Dependencies

Install required packages:

```bash
npm install axios
```

### 2. PayMongo API Configuration

Create PayMongo API client in your routes file:

```javascript
// server/routes.ts
import axios from 'axios';

const PAYMONGO_SECRET_KEY = process.env.PAYMONGO_SECRET_KEY;
const PAYMONGO_PUBLIC_KEY = process.env.PAYMONGO_PUBLIC_KEY;

if (!PAYMONGO_SECRET_KEY) {
  throw new Error("PAYMONGO_SECRET_KEY is required");
}

// PayMongo API helper
const paymongoApi = axios.create({
  baseURL: 'https://api.paymongo.com/v1',
  headers: {
    'Authorization': `Basic ${Buffer.from(PAYMONGO_SECRET_KEY + ':').toString('base64')}`,
    'Content-Type': 'application/json',
  },
});
```

### 3. Payment Intent Creation

Implement the upgrade endpoint:

```javascript
// server/routes.ts
app.post('/api/upgrade', isAuthenticated, async (req: any, res) => {
  try {
    const userId = req.user.claims.sub;
    const user = await storage.getUser(userId);
    
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    if (user.plan === 'pro' || user.plan === 'agency') {
      return res.status(400).json({ message: "User already has a premium plan" });
    }

    // Create PayMongo payment intent
    const paymentIntentData = {
      data: {
        attributes: {
          amount: 9900, // ₱99.00 in centavos
          currency: 'PHP',
          description: 'JuanCV Pro Plan Upgrade',
          statement_descriptor: 'JuanCV Pro',
          payment_method_allowed: [
            'card',
            'gcash',
            'grab_pay',
            'paymaya'
          ],
          metadata: {
            user_id: userId,
            plan: 'pro',
            email: user.email || '',
            name: `${user.firstName || ''} ${user.lastName || ''}`.trim()
          }
        }
      }
    };

    const paymentIntentResponse = await paymongoApi.post('/payment_intents', paymentIntentData);
    const paymentIntent = paymentIntentResponse.data.data;

    // Create checkout session for hosted payment page
    const checkoutSessionData = {
      data: {
        attributes: {
          cancel_url: `${req.protocol}://${req.get('host')}/billing?status=cancelled`,
          success_url: `${req.protocol}://${req.get('host')}/billing?status=success`,
          line_items: [
            {
              name: 'JuanCV Pro Plan',
              quantity: 1,
              amount: 9900,
              currency: 'PHP',
              description: 'Monthly subscription to JuanCV Pro Plan'
            }
          ],
          payment_method_types: ['card', 'gcash', 'grab_pay', 'paymaya'],
          metadata: {
            user_id: userId,
            plan: 'pro',
            payment_intent_id: paymentIntent.id
          }
        }
      }
    };

    const checkoutResponse = await paymongoApi.post('/checkout_sessions', checkoutSessionData);
    const checkoutSession = checkoutResponse.data.data;

    res.json({
      payment_intent_id: paymentIntent.id,
      checkout_url: checkoutSession.attributes.checkout_url,
      client_key: paymentIntent.attributes.client_key,
      status: paymentIntent.attributes.status
    });
  } catch (error: any) {
    console.error("Error creating PayMongo payment:", error);
    console.error("Error details:", error.response?.data);
    res.status(400).json({ 
      error: { 
        message: error.response?.data?.errors?.[0]?.detail || error.message 
      } 
    });
  }
});
```

### 4. Webhook Handler

Implement webhook endpoint to handle payment confirmations:

```javascript
// server/routes.ts
app.post('/api/webhook', async (req, res) => {
  try {
    const event = req.body;
    
    console.log('PayMongo webhook received:', event.data?.type);

    if (event.data?.type === 'payment_intent.payment.paid') {
      const paymentIntent = event.data.attributes.data;
      const metadata = paymentIntent.attributes.metadata;
      
      if (metadata.user_id && metadata.plan) {
        // Update user plan to pro
        await storage.updateUserPlan(metadata.user_id, metadata.plan);
        console.log(`Updated user ${metadata.user_id} to ${metadata.plan} plan`);
      }
    }

    res.json({ received: true });
  } catch (error) {
    console.error("PayMongo webhook error:", error);
    res.status(400).json({ error: "Webhook error" });
  }
});
```

## Frontend Implementation

### 1. API Request Helper

Update your API client to support the new PayMongo endpoints:

```javascript
// client/src/lib/queryClient.ts
export async function apiRequest(
  url: string,
  options: {
    method?: string;
    data?: unknown;
  } = {}
): Promise<any> {
  const { method = "GET", data } = options;
  
  const res = await fetch(url, {
    method,
    headers: data ? { "Content-Type": "application/json" } : {},
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  await throwIfResNotOk(res);
  return await res.json();
}
```

### 2. Billing Page Implementation

Create a billing page component:

```typescript
// client/src/pages/billing.tsx
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";

function UpgradeForm() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const upgradeToProMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('/api/upgrade', {
        method: 'POST',
      });
      return response;
    },
    onSuccess: (data) => {
      // Redirect to PayMongo checkout page
      window.location.href = data.checkout_url;
    },
    onError: (error: any) => {
      console.error('Upgrade failed:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create payment",
        variant: "destructive",
      });
      setIsLoading(false);
    },
  });

  const handleUpgrade = () => {
    setIsLoading(true);
    upgradeToProMutation.mutate();
  };

  return (
    <Button
      onClick={handleUpgrade}
      disabled={isLoading}
      className="w-full bg-amber-600 hover:bg-amber-700"
    >
      {isLoading ? 'Processing...' : 'Upgrade to Pro - ₱99/month'}
    </Button>
  );
}
```

### 3. Payment Status Handling

Handle payment status from URL parameters:

```typescript
// In your billing page component
useEffect(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const status = urlParams.get('status');
  
  if (status === 'success') {
    toast({
      title: "Payment Successful!",
      description: "Welcome to JuanCV Pro! Your upgrade is now active.",
    });
  } else if (status === 'cancelled') {
    toast({
      title: "Payment Cancelled",
      description: "Your payment was cancelled. You can try again anytime.",
      variant: "destructive",
    });
  }
  
  // Clear URL parameters
  if (status) {
    window.history.replaceState({}, document.title, window.location.pathname);
  }
}, []);
```

## Database Schema

### User Table Updates

Update your users table to include PayMongo-specific fields:

```sql
-- Add PayMongo columns to users table
ALTER TABLE users ADD COLUMN paymongo_customer_id VARCHAR;
ALTER TABLE users ADD COLUMN paymongo_subscription_id VARCHAR;

-- If migrating from Stripe, rename existing columns
ALTER TABLE users RENAME COLUMN stripe_customer_id TO paymongo_customer_id;
ALTER TABLE users RENAME COLUMN stripe_subscription_id TO paymongo_subscription_id;
```

### Drizzle Schema

```typescript
// shared/schema.ts
export const users = pgTable("users", {
  id: varchar("id").primaryKey().notNull(),
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  username: varchar("username").unique(),
  plan: varchar("plan", { enum: ["free", "pro", "agency"] }).default("free"),
  paymongoCustomerId: varchar("paymongo_customer_id"),
  paymongoSubscriptionId: varchar("paymongo_subscription_id"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});
```

### Storage Interface

Update your storage interface:

```typescript
// server/storage.ts
export interface IStorage {
  updateUserPaymongoInfo(userId: string, paymongoCustomerId: string, paymongoSubscriptionId: string): Promise<User>;
  updateUserPlan(userId: string, plan: "free" | "pro" | "agency"): Promise<User>;
  // ... other methods
}
```

## Testing

### 1. Test Payment Flow

1. Navigate to `/billing`
2. Click "Upgrade to Pro"
3. Should redirect to PayMongo checkout page
4. Complete payment using test card: `****************`
5. Should redirect back with success status

### 2. Test Webhook

1. Use ngrok or similar tool to expose local webhook endpoint
2. Configure webhook URL in PayMongo dashboard
3. Complete a test payment
4. Verify webhook receives `payment_intent.payment.paid` event
5. Verify user plan is updated in database

### 3. Test Payment Methods

PayMongo supports multiple payment methods:
- **Cards**: Visa, Mastercard, JCB
- **E-wallets**: GCash, GrabPay, PayMaya
- **Online Banking**: BPI, BDO, Metrobank, etc.

## Webhook Configuration

### 1. PayMongo Dashboard Setup

1. Go to PayMongo Dashboard → Developers → Webhooks
2. Create new webhook endpoint
3. Set URL to: `https://your-app-domain.com/api/webhook`
4. Select events: `payment_intent.payment.paid`
5. Set status to Active

### 2. Webhook Security

For production, implement webhook signature verification:

```javascript
// Verify webhook signature (optional but recommended)
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const computedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(computedSignature)
  );
}
```

## Error Handling

### Common Error Scenarios

1. **Invalid API Keys**: Verify keys are correct and not expired
2. **Network Issues**: Implement retry logic with exponential backoff
3. **Payment Failures**: Show user-friendly error messages
4. **Webhook Failures**: Log errors and implement retry mechanism

### Error Response Format

```typescript
interface PayMongoError {
  errors: Array<{
    code: string;
    detail: string;
    source: {
      pointer: string;
      parameter: string;
    };
  }>;
}
```

## Security Considerations

### 1. API Key Management

- Never expose secret keys in frontend code
- Use environment variables for API keys
- Rotate keys regularly
- Use test keys for development

### 2. Webhook Security

- Implement signature verification
- Use HTTPS endpoints only
- Validate webhook payloads
- Implement idempotency checks

### 3. Data Protection

- Encrypt sensitive payment data
- Implement proper access controls
- Log payment events for audit trails
- Follow PCI DSS guidelines

## Troubleshooting

### Common Issues

1. **"Invalid API Key"**
   - Check if using correct environment (test vs live)
   - Verify key format and permissions

2. **"Payment Intent Creation Failed"**
   - Validate amount (must be in centavos)
   - Check currency code (PHP)
   - Verify payment method support

3. **"Webhook Not Receiving Events"**
   - Check webhook URL accessibility
   - Verify webhook event configuration
   - Check server logs for errors

4. **"Redirect Not Working"**
   - Verify success/cancel URLs
   - Check URL encoding
   - Ensure proper HTTPS configuration

### Debug Tools

1. **PayMongo Dashboard**: Monitor transactions and events
2. **Webhook Logs**: Check webhook delivery status
3. **API Logs**: Review API request/response logs
4. **Browser Console**: Debug frontend integration

## Migration from Stripe

If migrating from Stripe, follow these steps:

1. **Update Dependencies**
   ```bash
   npm uninstall stripe @stripe/stripe-js @stripe/react-stripe-js
   npm install axios
   ```

2. **Database Migration**
   ```sql
   ALTER TABLE users RENAME COLUMN stripe_customer_id TO paymongo_customer_id;
   ALTER TABLE users RENAME COLUMN stripe_subscription_id TO paymongo_subscription_id;
   ```

3. **Code Updates**
   - Replace Stripe API calls with PayMongo equivalents
   - Update payment flow to use checkout sessions
   - Modify webhook handlers for PayMongo events

4. **Testing**
   - Test all payment flows thoroughly
   - Verify webhook functionality
   - Update any hardcoded references

## Support and Resources

- **PayMongo Documentation**: https://developers.paymongo.com/
- **API Reference**: https://developers.paymongo.com/reference
- **Support**: https://paymongo.com/support
- **Status Page**: https://status.paymongo.com/

---

## Implementation Checklist

- [ ] PayMongo account setup and verification
- [ ] API keys configured in environment
- [ ] Backend payment intent creation
- [ ] Frontend upgrade flow implementation
- [ ] Webhook endpoint configuration
- [ ] Database schema updates
- [ ] Payment status handling
- [ ] Error handling implementation
- [ ] Security measures in place
- [ ] Testing completed
- [ ] Production deployment ready

This documentation provides a complete guide for implementing PayMongo integration in your application. Follow the steps systematically and test thoroughly before deploying to production.