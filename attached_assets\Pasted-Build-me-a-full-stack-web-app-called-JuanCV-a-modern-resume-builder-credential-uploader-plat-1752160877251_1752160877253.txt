Build me a full-stack web app called **JuanCV** — a modern resume builder + credential uploader platform with shareable links and monetization tiers.

---

🧠 KEY FEATURES:
✅ Create an online resume (form-based builder)  
✅ Upload credentials: certs, IDs, diplomas (PDF, JPG, PNG)  
✅ Share a public profile (e.g. juancv.app/ceokath)  
✅ Download resume as PDF  
✅ Customize resume template (Pro only)  
✅ Enable monetization with Free, Pro, and Agency tiers

---

💻 TECH STACK:
Frontend: React + Tailwind CSS + React Router  
Backend: Node.js + Express  
Database: MongoDB Atlas  
File Storage: Cloudinary  
Auth: JWT  
PDF Export: html-pdf-node  
Payments: Stripe (or Xendit for PH compatibility)  
Deployment-ready on Replit, Vercel, or Render

---

📁 FILE STRUCTURE

/backend  
├── server.js  
├── config/db.js  
├── models/User.js  
├── models/Resume.js  
├── models/Credential.js  
├── routes/auth.js  
├── routes/resume.js  
├── routes/credential.js  
├── routes/payment.js  
└── middleware/authMiddleware.js  

/frontend  
├── App.jsx  
├── pages/Login.jsx  
├── pages/Register.jsx  
├── pages/Dashboard.jsx  
├── pages/PublicProfile.jsx  
├── pages/Billing.jsx  
├── components/ResumeForm.jsx  
├── components/CredentialUploader.jsx  
├── components/Navbar.jsx  
└── tailwind.config.js  

.env  
README.md  

---

🗂️ MONGODB SCHEMAS

User.js
- name, email, password (bcrypt)
- username (for shareable link)
- plan: free, pro, agency
- createdAt

Resume.js
- userId (ref)
- experience, education, skills, certifications
- summary, contact info

Credential.js
- userId
- fileUrl
- title, description
- visibility: public/private

---

🔐 AUTH (auth.js)
- JWT login/register
- Middleware for protected routes

---

📄 RESUME FEATURES
- Dynamic form builder
- PDF export with html-pdf-node
- Preview inside dashboard
- Branded PDF (Pro only)

---

📤 CREDENTIAL UPLOAD
- File input with Cloudinary upload
- List view in dashboard
- Limit uploads based on plan
- Preview on public profile (if public)

---

🔗 SHAREABLE PROFILE (React Router)
- Route `/[username]` for public profile
- Displays resume, image, credentials
- QR code generator using `qrcode.react`

---

📥 DOWNLOAD PDF
- Express route `/download/:username`
- Render CV as HTML → convert to PDF
- Option to watermark PDFs for Free users

---

💳 MONETIZATION TIERS

1. 🆓 **Free Plan**
- 1 resume template
- Up to 3 credentials
- Public profile + QR
- Watermarked PDF

2. 💼 **Pro Plan** (₱99/mo or ₱499/year)
- Unlimited resume edits
- 20 credential uploads
- Premium PDF templates
- No watermark
- Custom profile link: `yourname.mycv.ph`
- Basic analytics

3. 🏢 **Agency Plan** (₱999/mo)
- 10 managed accounts
- Bulk resume download
- Admin dashboard
- Custom branding and templates

In `User` model:
```js
userPlan: {
  type: String,
  enum: ['free', 'pro', 'agency'],
  default: 'free'
}
