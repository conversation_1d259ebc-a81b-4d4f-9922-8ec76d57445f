import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { CV_TEMPLATES, CVTemplate, DEFAULT_TEMPLATE, TEMPLATE_STORAGE_KEY, getTemplatesByCategory } from "@/lib/cvTemplates";
import { Eye, Check, Palette } from "lucide-react";

interface TemplateSelectorProps {
  selectedTemplate: CVTemplate;
  onTemplateSelect: (template: CVTemplate) => void;
  onGeneratePDF: (templateId: string) => void;
  isGenerating?: boolean;
}

export default function TemplateSelector({ 
  selectedTemplate, 
  onTemplateSelect, 
  onGeneratePDF, 
  isGenerating = false 
}: TemplateSelectorProps) {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState<CVTemplate | null>(null);
  const [activeCategory, setActiveCategory] = useState<'all' | 'modern' | 'minimalist' | 'creative' | 'classic'>('all');

  // Filter templates based on active category
  const filteredTemplates = activeCategory === 'all' 
    ? CV_TEMPLATES 
    : getTemplatesByCategory(activeCategory as CVTemplate['category']);

  const handleTemplateSelect = (template: CVTemplate) => {
    onTemplateSelect(template);
    // Save to localStorage
    localStorage.setItem(TEMPLATE_STORAGE_KEY, template.id);
  };

  const handlePreview = (template: CVTemplate) => {
    setPreviewTemplate(template);
    setIsPreviewOpen(true);
  };

  const TemplateCard = ({ template }: { template: CVTemplate }) => {
    const isSelected = selectedTemplate.id === template.id;
    
    return (
      <Card 
        className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${
          isSelected 
            ? 'ring-2 ring-yellow-500 bg-yellow-50' 
            : 'hover:ring-1 hover:ring-gray-300'
        }`}
        onClick={() => handleTemplateSelect(template)}
      >
        <CardContent className="p-0">
          {/* Template Thumbnail */}
          <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-lg overflow-hidden">
            <TemplateThumbnail template={template} />
            
            {/* Selection Indicator */}
            {isSelected && (
              <div className="absolute top-2 right-2 bg-yellow-500 text-white rounded-full p-1">
                <Check className="w-4 h-4" />
              </div>
            )}
            
            {/* Preview Button */}
            <Button
              variant="secondary"
              size="sm"
              className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                handlePreview(template);
              }}
            >
              <Eye className="w-4 h-4 mr-1" />
              Preview
            </Button>
          </div>
          
          {/* Template Info */}
          <div className="p-4">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-semibold text-gray-900">{template.name}</h3>
              <Badge variant="outline" className="text-xs">
                {template.category}
              </Badge>
            </div>
            <p className="text-sm text-gray-600 mb-3">{template.description}</p>
            
            {/* Color Palette */}
            <div className="flex items-center gap-2">
              <Palette className="w-4 h-4 text-gray-400" />
              <div className="flex gap-1">
                <div 
                  className="w-4 h-4 rounded-full border border-gray-300" 
                  style={{ backgroundColor: template.primaryColor }}
                />
                <div 
                  className="w-4 h-4 rounded-full border border-gray-300" 
                  style={{ backgroundColor: template.secondaryColor }}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Your CV Template</h2>
        <p className="text-gray-600">Select a template that matches your professional style</p>
      </div>

      {/* Category Tabs */}
      <Tabs value={activeCategory} onValueChange={(value) => setActiveCategory(value as any)}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="modern">Modern</TabsTrigger>
          <TabsTrigger value="minimalist">Minimalist</TabsTrigger>
          <TabsTrigger value="creative">Creative</TabsTrigger>
          <TabsTrigger value="classic">Classic</TabsTrigger>
        </TabsList>

        <TabsContent value={activeCategory} className="mt-6">
          {/* Template Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredTemplates.map((template) => (
              <div key={template.id} className="group">
                <TemplateCard template={template} />
              </div>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Selected Template & Generate Button */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-semibold text-gray-900">Selected Template</h3>
            <p className="text-sm text-gray-600">
              {selectedTemplate.name} - {selectedTemplate.description}
            </p>
          </div>
          <Button 
            onClick={() => onGeneratePDF(selectedTemplate.id)}
            disabled={isGenerating}
            className="bg-yellow-500 hover:bg-yellow-600"
          >
            {isGenerating ? 'Generating...' : 'Generate CV'}
          </Button>
        </div>
      </div>

      {/* Preview Modal */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Template Preview: {previewTemplate?.name}</DialogTitle>
          </DialogHeader>
          {previewTemplate && (
            <div className="space-y-4">
              <div className="h-96 bg-gray-50 rounded-lg flex items-center justify-center">
                <TemplateThumbnail template={previewTemplate} isPreview />
              </div>
              <div className="flex justify-between">
                <Button 
                  variant="outline" 
                  onClick={() => setIsPreviewOpen(false)}
                >
                  Close
                </Button>
                <Button 
                  onClick={() => {
                    handleTemplateSelect(previewTemplate);
                    setIsPreviewOpen(false);
                  }}
                  className="bg-yellow-500 hover:bg-yellow-600"
                >
                  Use This Template
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Template Thumbnail Component
function TemplateThumbnail({ template, isPreview = false }: { template: CVTemplate; isPreview?: boolean }) {
  const scale = isPreview ? 1 : 0.8;
  const width = isPreview ? 400 : 200;
  const height = isPreview ? 500 : 250;

  return (
    <div 
      className="w-full h-full flex items-center justify-center"
      style={{ transform: `scale(${scale})` }}
    >
      <svg width={width} height={height} viewBox="0 0 400 500" className="border border-gray-300 bg-white rounded">
        {/* Template-specific layouts */}
        {template.layout === 'sidebar-left' && (
          <>
            {/* Sidebar */}
            <rect x="0" y="0" width="120" height="500" fill={template.secondaryColor} />
            <rect x="10" y="20" width="100" height="60" fill={template.primaryColor} opacity="0.3" />
            <rect x="10" y="100" width="80" height="8" fill={template.primaryColor} />
            <rect x="10" y="120" width="60" height="6" fill="#666" />
            
            {/* Main content */}
            <rect x="140" y="20" width="240" height="20" fill={template.primaryColor} />
            <rect x="140" y="60" width="200" height="8" fill="#666" />
            <rect x="140" y="80" width="180" height="6" fill="#999" />
            <rect x="140" y="120" width="220" height="12" fill={template.primaryColor} />
            <rect x="140" y="150" width="200" height="6" fill="#666" />
          </>
        )}
        
        {template.layout === 'single-column' && (
          <>
            <rect x="50" y="20" width="300" height="20" fill={template.primaryColor} />
            <rect x="50" y="60" width="250" height="8" fill="#666" />
            <rect x="50" y="80" width="200" height="6" fill="#999" />
            <rect x="50" y="120" width="280" height="12" fill={template.primaryColor} />
            <rect x="50" y="150" width="250" height="6" fill="#666" />
            <rect x="50" y="180" width="270" height="6" fill="#666" />
          </>
        )}
        
        {template.layout === 'two-column' && (
          <>
            <rect x="20" y="20" width="360" height="20" fill={template.primaryColor} />
            <rect x="20" y="60" width="170" height="12" fill={template.primaryColor} />
            <rect x="20" y="90" width="150" height="6" fill="#666" />
            <rect x="210" y="60" width="170" height="12" fill={template.primaryColor} />
            <rect x="210" y="90" width="150" height="6" fill="#666" />
          </>
        )}
        
        {template.layout === 'sidebar-right' && (
          <>
            {/* Main content */}
            <rect x="20" y="20" width="240" height="20" fill={template.primaryColor} />
            <rect x="20" y="60" width="200" height="8" fill="#666" />
            <rect x="20" y="80" width="180" height="6" fill="#999" />
            <rect x="20" y="120" width="220" height="12" fill={template.primaryColor} />
            <rect x="20" y="150" width="200" height="6" fill="#666" />
            
            {/* Sidebar */}
            <rect x="280" y="0" width="120" height="500" fill={template.secondaryColor} />
            <rect x="290" y="20" width="100" height="60" fill={template.primaryColor} opacity="0.3" />
            <rect x="290" y="100" width="80" height="8" fill={template.primaryColor} />
            <rect x="290" y="120" width="60" height="6" fill="#666" />
          </>
        )}
        
        {/* Profile photo placeholder */}
        <circle cx="350" cy="50" r="20" fill="#e5e7eb" stroke="#d1d5db" strokeWidth="2" />
      </svg>
    </div>
  );
}