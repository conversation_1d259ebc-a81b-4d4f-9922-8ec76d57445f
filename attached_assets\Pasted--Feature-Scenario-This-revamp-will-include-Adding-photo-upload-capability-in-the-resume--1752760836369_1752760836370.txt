* Feature
** Scenario: This revamp will include:
*** Adding photo upload capability in the resume section
*** Improving the layout and experience of all other user profile sections
*** Making the “Save Changes” and interaction elements more appealing and responsive
*** Creating a visually engaging dashboard for profile activity

* Revamped Sections and UX Goals
** Resume Builder (Main Profile)
*** Add a circular profile photo upload component
*** Show live image preview
*** Enforce file type (JPG/PNG) and size limit
*** Provide feedback (uploading..., success, retry)

** Credentials
*** Use card-style input fields with icons for each credential (e.g., license #, certifications)
*** Add expandable sections for adding multiple entries
*** Support validation and feedback (✓ / ✕ icons)

** Skills
*** Add tag input UI to enter skills dynamically
*** Show animated chips/tags with delete option
*** Option to reorder or group by category (e.g., "Soft Skills", "Tech Skills")

** Public Profile
*** Toggle to show/hide profile publicly
*** Add a “copy profile link” button
*** Display a preview card of how your public profile looks

** Work Experience
*** Use a timeline component for each job history
*** Allow editing inline with modal or collapsible sections

** Education
*** Card layout with degree title, institution, and date
*** Use icons for course, school, and duration
*** Animated transitions for add/edit/remove actions

** Recent Profile Views
*** Create an activity feed style layout
*** Show who viewed your profile, when, and from where (if available)
*** Display avatar, name, and timestamp

** Recent Downloads
*** List style UI with:
**** Date of download
**** Version of resume
**** “Open” and “Delete” actions
*** Add filters (e.g., “last 7 days”, “last 30 days”)

** UI/UX Requirements
*** Clean and consistent layout (use a grid or sidebar-based layout)
*** Fixed save button with spinner + success toast
*** Responsive design (mobile, tablet, desktop)
*** Use subtle animations for transitions and loading states
*** Implement dark mode toggle