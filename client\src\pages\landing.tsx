import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent } from "@/components/ui/card";
import Navbar from "@/components/navbar";
import {
  Edit,
  CloudUpload,
  Share,
  Eye,
  Download,
  Tag,
  Crown,
  Check,
  Facebook,
  Twitter,
  Linkedin,
} from "lucide-react";

export default function Landing() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-white to-pink-50 py-20 lg:py-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Build Your <span className="text-yellow-500">Professional</span>
              <br />
              Resume in Minutes
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Create stunning resumes, upload credentials, and share your
              professional profile with a personalized link. Trusted by
              thousands of professionals worldwide.
            </p>
            <div className="flex justify-center px-4 mt-10">
              <Button
                size="lg"
                className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white px-12 py-6 text-xl font-bold transform hover:scale-110 hover:shadow-2xl transition-all duration-300 shadow-lg w-full sm:w-auto max-w-md sm:max-w-none rounded-xl border-0 focus:ring-4 focus:ring-yellow-300 focus:ring-opacity-50 relative overflow-hidden group"
                onClick={() => (window.location.href = "/api/login")}
              >
                <span className="relative z-10">Start Building for Free →</span>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
              </Button>
            </div>

            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Edit className="text-yellow-600 text-xl" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  Easy Builder
                </h3>
                <p className="text-gray-600 text-sm">
                  Intuitive form-based resume creation with professional
                  templates
                </p>
              </div>
              <div className="text-center">
                <div className="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CloudUpload className="text-yellow-600 text-xl" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  Upload Credentials
                </h3>
                <p className="text-gray-600 text-sm">
                  Showcase your certificates, diplomas, and professional
                  documents
                </p>
              </div>
              <div className="text-center">
                <div className="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Share className="text-yellow-600 text-xl" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  Share Profile
                </h3>
                <p className="text-gray-600 text-sm">
                  Get your personalized link and QR code for easy sharing
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Choose Your Plan
            </h2>
            <p className="text-xl text-gray-600">
              Start free and upgrade as your career grows
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Free Plan */}
            <Card className="relative">
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    Free
                  </h3>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">₱0</span>
                    <span className="text-gray-600">/month</span>
                  </div>
                  <p className="text-gray-600">Perfect for getting started</p>
                </div>
                <ul className="space-y-4 mb-8">
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span className="text-gray-700">1 resume template</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span className="text-gray-700">Up to 3 credentials</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span className="text-gray-700">
                      Public profile + QR code
                    </span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span className="text-gray-700">
                      PDF download (watermarked)
                    </span>
                  </li>
                </ul>
                <Button
                  variant="outline"
                  className="w-full py-3 text-lg font-semibold border-2 border-yellow-500 text-yellow-600 hover:bg-yellow-500 hover:text-white transition-all duration-200"
                  onClick={() => (window.location.href = "/api/login")}
                >
                  Start Building Free →
                </Button>
              </CardContent>
            </Card>

            {/* Pro Plan */}
            <Card className="relative border-2 border-yellow-500 shadow-lg">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-yellow-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </span>
              </div>
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Pro</h3>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">
                      ₱99
                    </span>
                    <span className="text-gray-600">/month</span>
                  </div>
                  <p className="text-gray-600">For serious professionals</p>
                </div>
                <ul className="space-y-4 mb-8">
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span className="text-gray-700">
                      Unlimited resume edits
                    </span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span className="text-gray-700">20 credential uploads</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span className="text-gray-700">Premium PDF templates</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span className="text-gray-700">No watermark</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span className="text-gray-700">Custom profile link</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span className="text-gray-700">Basic analytics</span>
                  </li>
                </ul>
                <Button
                  className="w-full bg-yellow-500 hover:bg-yellow-600 py-3 text-lg font-semibold transition-all duration-200 shadow-md hover:shadow-lg"
                  onClick={() => (window.location.href = "/api/login")}
                >
                  Start Pro Trial →
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold mb-4">
                Juan<span className="text-yellow-500">CV</span>
              </h3>
              <p className="text-gray-300 mb-6 max-w-md">
                The modern way to build, share, and showcase your professional
                resume and credentials. Trusted by thousands of professionals
                across the Philippines.
              </p>
              <div className="flex space-x-4">
                <Facebook className="text-gray-400 hover:text-white transition-colors cursor-pointer" />
                <Twitter className="text-gray-400 hover:text-white transition-colors cursor-pointer" />
                <Linkedin className="text-gray-400 hover:text-white transition-colors cursor-pointer" />
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-300">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Pricing
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Templates
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Examples
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-300">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Contact Us
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Privacy Policy
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Terms of Service
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center">
            <p className="text-gray-400">
              © 2025 JuanCV. All rights reserved. Made with ❤️ in the
              Philippines.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
