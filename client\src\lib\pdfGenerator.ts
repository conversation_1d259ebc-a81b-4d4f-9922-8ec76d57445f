import jsPDF from "jspdf";
import { CVTemplate, getTemplateById, DEFAULT_TEMPLATE } from "./cvTemplates";

export interface PDFData {
  user: {
    firstName: string;
    lastName: string;
    email: string;
    plan?: string;
    profileImageUrl?: string;
  };
  resume?: {
    title?: string;
    summary?: string;
    phone?: string;
    location?: string;
    website?: string;
    profilePhotoUrl?: string;
    skills?: string[];
    experience?: Array<{
      title: string;
      company: string;
      duration: string;
      description: string;
    }>;
    education?: Array<{
      degree: string;
      school: string;
      year: string;
      description?: string;
    }>;
  };
  credentials?: Array<{
    title: string;
    description?: string;
    issuer?: string;
    issuedDate?: string;
  }>;
}

// Helper function to convert hex to RGB
function hexToRgb(hex: string): { r: number; g: number; b: number } {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : { r: 0, g: 0, b: 0 };
}

export function generatePDF(data: PDFData, templateId?: string): void {
  const { user, resume, credentials } = data;
  const template = templateId ? getTemplateById(templateId) || DEFAULT_TEMPLATE : DEFAULT_TEMPLATE;
  const doc = new jsPDF();

  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  let yPos = 20;

  // Set template colors
  const primaryColor = hexToRgb(template.primaryColor);
  const secondaryColor = hexToRgb(template.secondaryColor);

  // Generate PDF based on template layout
  switch (template.layout) {
    case 'sidebar-left':
      generateSidebarLeftPDF(doc, data, template, primaryColor, secondaryColor);
      break;
    case 'sidebar-right':
      generateSidebarRightPDF(doc, data, template, primaryColor, secondaryColor);
      break;
    case 'two-column':
      generateTwoColumnPDF(doc, data, template, primaryColor, secondaryColor);
      break;
    case 'single-column':
    default:
      generateSingleColumnPDF(doc, data, template, primaryColor, secondaryColor);
      break;
  }

  // Save the PDF
  const firstName = user.firstName || 'Professional';
  const lastName = user.lastName || '';
  const fileName = `${firstName}_${lastName}_CV_${template.name.replace(/\s+/g, '_')}.pdf`.replace(/_{2,}/g, '_').replace(/_+$/, '');
  doc.save(fileName);
}

// Single Column Layout (Original/Default)
function generateSingleColumnPDF(
  doc: jsPDF, 
  data: PDFData, 
  template: CVTemplate, 
  primaryColor: { r: number; g: number; b: number },
  secondaryColor: { r: number; g: number; b: number }
): void {
  const { user, resume, credentials } = data;
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  let yPos = 20;

  // Add watermark for free users
  const isPro = user.plan === "pro" || user.plan === "agency";
  if (!isPro) {
    doc.setTextColor(200, 200, 200);
    doc.setFontSize(50);
    doc.text("JUANCV FREE", pageWidth / 2, pageHeight / 2, {
      align: "center",
      angle: 45,
    });
    doc.setTextColor(0, 0, 0); // Reset color
  }

  // Add profile photo if available with proper sizing and alignment
  let photoPlaced = false;
  const photoSrc = resume?.profilePhotoUrl || user?.profileImageUrl;

  if (photoSrc) {
    try {
      // Fixed photo dimensions (120x120px equivalent in mm)
      const photoSize = 32; // 120px ≈ 32mm at 96 DPI
      const photoX = pageWidth - photoSize - 15; // 15mm margin from right
      const photoY = 15; // 15mm from top

      // Create a subtle shadow background
      doc.setFillColor(0, 0, 0, 0.1); // Light shadow
      doc.roundedRect(photoX + 1, photoY + 1, photoSize, photoSize, 2, 2, "F");

      // Create a white background for the photo
      doc.setFillColor(255, 255, 255);
      doc.roundedRect(photoX, photoY, photoSize, photoSize, 2, 2, "F");

      // Create a border around the photo
      doc.setDrawColor(229, 231, 235); // Light gray border
      doc.setLineWidth(0.5);
      doc.roundedRect(photoX, photoY, photoSize, photoSize, 2, 2, "S");

      // Add the image with proper scaling and clipping
      doc.addImage(
        photoSrc,
        "JPEG",
        photoX + 1, // Small padding from border
        photoY + 1,
        photoSize - 2, // Adjust for padding
        photoSize - 2,
        "",
        "FAST",
      );

      photoPlaced = true;
    } catch (error) {
      console.warn("Failed to add profile photo to PDF:", error);
    }
  }

  // Adjust header layout based on photo placement
  const headerLeftMargin = 20;
  const headerRightMargin = photoPlaced ? pageWidth - 70 : pageWidth - 20; // Leave space for photo
  const headerWidth = headerRightMargin - headerLeftMargin;

  // Header - adjusted for photo placement
  doc.setFontSize(24);
  doc.setFont(undefined, "bold");
  const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Professional';

  if (photoPlaced) {
    // Left-align header text when photo is present
    doc.text(fullName, headerLeftMargin, yPos);
    yPos += 10;

    // Contact info - left aligned
    doc.setFontSize(12);
    doc.setFont(undefined, "normal");
    const contactInfo = [
      user.email,
      resume?.phone,
      resume?.location,
      resume?.website,
    ]
      .filter(Boolean)
      .join(" • ");

    // Split long contact info into multiple lines if needed
    const contactLines = doc.splitTextToSize(contactInfo, headerWidth);
    doc.text(contactLines, headerLeftMargin, yPos);
    yPos += contactLines.length * 5;
  } else {
    // Center-align header text when no photo
    doc.text(fullName, pageWidth / 2, yPos, { align: "center" });
    yPos += 10;

    // Contact info - centered
    doc.setFontSize(12);
    doc.setFont(undefined, "normal");
    const contactInfo = [
      user.email,
      resume?.phone,
      resume?.location,
      resume?.website,
    ]
      .filter(Boolean)
      .join(" • ");

    doc.text(contactInfo, pageWidth / 2, yPos, { align: "center" });
    yPos += 5;
  }

  // Ensure proper spacing after header
  yPos = Math.max(yPos, photoPlaced ? 55 : yPos + 5);

  // Line separator
  doc.setDrawColor(245, 158, 11); // Yellow color
  doc.setLineWidth(1);
  doc.line(20, yPos, pageWidth - 20, yPos);
  yPos += 15;

  // Helper function to add section
  function addSection(title: string, content: () => void) {
    if (yPos > pageHeight - 30) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(16);
    doc.setFont(undefined, "bold");
    doc.text(title, 20, yPos);
    yPos += 8;

    // Section underline
    doc.setDrawColor(229, 231, 235);
    doc.setLineWidth(0.5);
    doc.line(20, yPos - 2, pageWidth - 20, yPos - 2);
    yPos += 5;

    doc.setFontSize(11);
    doc.setFont(undefined, "normal");
    content();
    yPos += 10;
  }

  // Summary
  if (resume?.summary) {
    addSection("Summary", () => {
      const summaryLines = doc.splitTextToSize(resume.summary!, pageWidth - 40);
      doc.text(summaryLines, 20, yPos);
      yPos += summaryLines.length * 5;
    });
  }

  // Skills
  if (resume?.skills?.length) {
    addSection("Skills", () => {
      const skillsText = resume.skills!.join(", ");
      const skillsLines = doc.splitTextToSize(skillsText, pageWidth - 40);
      doc.text(skillsLines, 20, yPos);
      yPos += skillsLines.length * 5;
    });
  }

  // Experience
  if (resume?.experience?.length) {
    addSection("Experience", () => {
      resume.experience!.forEach((exp) => {
        if (yPos > pageHeight - 40) {
          doc.addPage();
          yPos = 20;
        }

        doc.setFont(undefined, "bold");
        doc.text(exp.title, 20, yPos);
        yPos += 5;

        doc.setFont(undefined, "italic");
        doc.text(`${exp.company} • ${exp.duration}`, 20, yPos);
        yPos += 5;

        doc.setFont(undefined, "normal");
        const descLines = doc.splitTextToSize(exp.description, pageWidth - 40);
        doc.text(descLines, 20, yPos);
        yPos += descLines.length * 5 + 5;
      });
    });
  }

  // Education
  if (resume?.education?.length) {
    addSection("Education", () => {
      resume.education!.forEach((edu) => {
        if (yPos > pageHeight - 30) {
          doc.addPage();
          yPos = 20;
        }

        doc.setFont(undefined, "bold");
        doc.text(edu.degree, 20, yPos);
        yPos += 5;

        doc.setFont(undefined, "italic");
        doc.text(`${edu.school} • ${edu.year}`, 20, yPos);
        yPos += 5;

        if (edu.description) {
          doc.setFont(undefined, "normal");
          const descLines = doc.splitTextToSize(
            edu.description,
            pageWidth - 40,
          );
          doc.text(descLines, 20, yPos);
          yPos += descLines.length * 5;
        }
        yPos += 5;
      });
    });
  }

  // Credentials
  if (credentials?.length) {
    addSection("Credentials", () => {
      credentials.forEach((cred) => {
        if (yPos > pageHeight - 40) {
          doc.addPage();
          yPos = 20;
        }

        doc.setFont(undefined, "bold");
        doc.text(cred.title, 20, yPos);
        yPos += 5;

        if (cred.issuer || cred.issuedDate) {
          doc.setFont(undefined, "italic");
          const credInfo = [cred.issuer, cred.issuedDate]
            .filter(Boolean)
            .join(" • ");
          doc.text(credInfo, 20, yPos);
          yPos += 5;
        }

        if (cred.description) {
          doc.setFont(undefined, "normal");
          const descLines = doc.splitTextToSize(
            cred.description,
            pageWidth - 40,
          );
          doc.text(descLines, 20, yPos);
          yPos += descLines.length * 5;
        }

        // Add clickable link to view credential file
        // if (cred.fileUrl) {
        //   const linkText = `🔗 View Credential`;
        //   const linkY = yPos + 3;

        //   // Set link color (blue) and underline style
        //   doc.setTextColor(0, 100, 200);
        //   doc.setFont(undefined, "normal");
        //   doc.text(linkText, 20, linkY);

        //   // Add underline to make it look like a clickable link
        //   const linkWidth = doc.getTextWidth(linkText);
        //   doc.setDrawColor(0, 100, 200);
        //   doc.setLineWidth(0.1);
        //   doc.line(20, linkY + 1, 20 + linkWidth, linkY + 1);

        //   // Create clickable link that opens the file
        //   doc.link(20, linkY - 3, linkWidth, 6, { url: cred.fileUrl });

        //   // Reset colors
        //   doc.setTextColor(0, 0, 0);
        //   doc.setDrawColor(0, 0, 0);
        //   yPos += 8;
        // }

        yPos += 5;
      });
    });
  }

  // Footer
  const totalPages = doc.internal.pages.length - 1;
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(150, 150, 150);
    doc.text(
      "Generated with JuanCV - Premium Resume Builder",
      pageWidth / 2,
      pageHeight - 10,
      { align: "center" },
    );
  }

}

// Sidebar Left Layout
function generateSidebarLeftPDF(
  doc: jsPDF, 
  data: PDFData, 
  template: CVTemplate, 
  primaryColor: { r: number; g: number; b: number },
  secondaryColor: { r: number; g: number; b: number }
): void {
  const { user, resume, credentials } = data;
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  
  // Add watermark for free users
  const isPro = user.plan === "pro" || user.plan === "agency";
  if (!isPro) {
    doc.setTextColor(200, 200, 200);
    doc.setFontSize(50);
    doc.text("JUANCV FREE", pageWidth / 2, pageHeight / 2, { align: "center", angle: 45 });
    doc.setTextColor(0, 0, 0);
  }

  // Sidebar (left side)
  const sidebarWidth = 70;
  doc.setFillColor(secondaryColor.r, secondaryColor.g, secondaryColor.b);
  doc.rect(0, 0, sidebarWidth, pageHeight, "F");
  
  let sidebarY = 20;
  const mainContentX = sidebarWidth + 10;
  
  // Profile photo in sidebar
  const photoSrc = resume?.profilePhotoUrl || user?.profileImageUrl;
  if (photoSrc) {
    try {
      const photoSize = 40;
      const photoX = (sidebarWidth - photoSize) / 2;
      doc.addImage(photoSrc, "JPEG", photoX, sidebarY, photoSize, photoSize);
      sidebarY += photoSize + 10;
    } catch (error) {
      console.warn("Failed to add profile photo:", error);
    }
  }
  
  // Contact info in sidebar
  doc.setFontSize(8);
  doc.setTextColor(primaryColor.r, primaryColor.g, primaryColor.b);
  const contactInfo = [user.email, resume?.phone, resume?.location, resume?.website].filter(Boolean);
  contactInfo.forEach(info => {
    const lines = doc.splitTextToSize(info, sidebarWidth - 10);
    doc.text(lines, 5, sidebarY);
    sidebarY += lines.length * 4 + 2;
  });
  
  // Skills in sidebar
  if (resume?.skills?.length) {
    sidebarY += 5;
    doc.setFontSize(10);
    doc.setFont(undefined, "bold");
    doc.text("SKILLS", 5, sidebarY);
    sidebarY += 8;
    
    doc.setFontSize(8);
    doc.setFont(undefined, "normal");
    resume.skills.forEach(skill => {
      const lines = doc.splitTextToSize(skill, sidebarWidth - 10);
      doc.text(lines, 5, sidebarY);
      sidebarY += lines.length * 4 + 1;
    });
  }
  
  // Main content
  let mainY = 20;
  doc.setTextColor(0, 0, 0);
  
  // Header
  doc.setFontSize(24);
  doc.setFont(undefined, "bold");
  const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Professional';
  doc.text(fullName, mainContentX, mainY);
  mainY += 10;
  
  if (resume?.title) {
    doc.setFontSize(14);
    doc.setTextColor(primaryColor.r, primaryColor.g, primaryColor.b);
    doc.text(resume.title, mainContentX, mainY);
    mainY += 10;
  }
  
  doc.setTextColor(0, 0, 0);
  
  // Summary
  if (resume?.summary) {
    mainY += 5;
    doc.setFontSize(12);
    doc.setFont(undefined, "bold");
    doc.text("SUMMARY", mainContentX, mainY);
    mainY += 8;
    
    doc.setFontSize(10);
    doc.setFont(undefined, "normal");
    const summaryLines = doc.splitTextToSize(resume.summary, pageWidth - mainContentX - 20);
    doc.text(summaryLines, mainContentX, mainY);
    mainY += summaryLines.length * 4 + 10;
  }
  
  // Experience
  if (resume?.experience?.length) {
    doc.setFontSize(12);
    doc.setFont(undefined, "bold");
    doc.text("EXPERIENCE", mainContentX, mainY);
    mainY += 8;
    
    resume.experience.forEach((exp) => {
      if (mainY > pageHeight - 30) {
        doc.addPage();
        mainY = 20;
      }
      
      doc.setFontSize(11);
      doc.setFont(undefined, "bold");
      doc.text(exp.title, mainContentX, mainY);
      mainY += 5;
      
      doc.setFontSize(9);
      doc.setFont(undefined, "italic");
      doc.text(`${exp.company} • ${exp.duration}`, mainContentX, mainY);
      mainY += 5;
      
      doc.setFontSize(9);
      doc.setFont(undefined, "normal");
      const descLines = doc.splitTextToSize(exp.description, pageWidth - mainContentX - 20);
      doc.text(descLines, mainContentX, mainY);
      mainY += descLines.length * 4 + 8;
    });
  }
  
  // Education
  if (resume?.education?.length) {
    mainY += 5;
    doc.setFontSize(12);
    doc.setFont(undefined, "bold");
    doc.text("EDUCATION", mainContentX, mainY);
    mainY += 8;
    
    resume.education.forEach((edu) => {
      if (mainY > pageHeight - 30) {
        doc.addPage();
        mainY = 20;
      }
      
      doc.setFontSize(11);
      doc.setFont(undefined, "bold");
      doc.text(edu.degree, mainContentX, mainY);
      mainY += 5;
      
      doc.setFontSize(9);
      doc.setFont(undefined, "italic");
      doc.text(`${edu.school} • ${edu.year}`, mainContentX, mainY);
      mainY += 5;
      
      if (edu.description) {
        doc.setFontSize(9);
        doc.setFont(undefined, "normal");
        const descLines = doc.splitTextToSize(edu.description, pageWidth - mainContentX - 20);
        doc.text(descLines, mainContentX, mainY);
        mainY += descLines.length * 4;
      }
      mainY += 8;
    });
  }
}

// Sidebar Right Layout
function generateSidebarRightPDF(
  doc: jsPDF, 
  data: PDFData, 
  template: CVTemplate, 
  primaryColor: { r: number; g: number; b: number },
  secondaryColor: { r: number; g: number; b: number }
): void {
  const { user, resume, credentials } = data;
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  
  // Add watermark for free users
  const isPro = user.plan === "pro" || user.plan === "agency";
  if (!isPro) {
    doc.setTextColor(200, 200, 200);
    doc.setFontSize(50);
    doc.text("JUANCV FREE", pageWidth / 2, pageHeight / 2, { align: "center", angle: 45 });
    doc.setTextColor(0, 0, 0);
  }

  // Sidebar (right side)
  const sidebarWidth = 70;
  const sidebarX = pageWidth - sidebarWidth;
  doc.setFillColor(secondaryColor.r, secondaryColor.g, secondaryColor.b);
  doc.rect(sidebarX, 0, sidebarWidth, pageHeight, "F");
  
  let sidebarY = 20;
  const mainContentWidth = sidebarX - 20;
  
  // Profile photo in sidebar
  const photoSrc = resume?.profilePhotoUrl || user?.profileImageUrl;
  if (photoSrc) {
    try {
      const photoSize = 40;
      const photoX = sidebarX + (sidebarWidth - photoSize) / 2;
      doc.addImage(photoSrc, "JPEG", photoX, sidebarY, photoSize, photoSize);
      sidebarY += photoSize + 10;
    } catch (error) {
      console.warn("Failed to add profile photo:", error);
    }
  }
  
  // Contact info in sidebar
  doc.setFontSize(8);
  doc.setTextColor(primaryColor.r, primaryColor.g, primaryColor.b);
  const contactInfo = [user.email, resume?.phone, resume?.location, resume?.website].filter(Boolean);
  contactInfo.forEach(info => {
    const lines = doc.splitTextToSize(info, sidebarWidth - 10);
    doc.text(lines, sidebarX + 5, sidebarY);
    sidebarY += lines.length * 4 + 2;
  });
  
  // Skills in sidebar
  if (resume?.skills?.length) {
    sidebarY += 5;
    doc.setFontSize(10);
    doc.setFont(undefined, "bold");
    doc.text("SKILLS", sidebarX + 5, sidebarY);
    sidebarY += 8;
    
    doc.setFontSize(8);
    doc.setFont(undefined, "normal");
    resume.skills.forEach(skill => {
      const lines = doc.splitTextToSize(skill, sidebarWidth - 10);
      doc.text(lines, sidebarX + 5, sidebarY);
      sidebarY += lines.length * 4 + 1;
    });
  }
  
  // Main content
  let mainY = 20;
  doc.setTextColor(0, 0, 0);
  
  // Header
  doc.setFontSize(24);
  doc.setFont(undefined, "bold");
  const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Professional';
  doc.text(fullName, 20, mainY);
  mainY += 10;
  
  if (resume?.title) {
    doc.setFontSize(14);
    doc.setTextColor(primaryColor.r, primaryColor.g, primaryColor.b);
    doc.text(resume.title, 20, mainY);
    mainY += 10;
  }
  
  doc.setTextColor(0, 0, 0);
  
  // Summary
  if (resume?.summary) {
    mainY += 5;
    doc.setFontSize(12);
    doc.setFont(undefined, "bold");
    doc.text("SUMMARY", 20, mainY);
    mainY += 8;
    
    doc.setFontSize(10);
    doc.setFont(undefined, "normal");
    const summaryLines = doc.splitTextToSize(resume.summary, mainContentWidth - 20);
    doc.text(summaryLines, 20, mainY);
    mainY += summaryLines.length * 4 + 10;
  }
  
  // Experience
  if (resume?.experience?.length) {
    doc.setFontSize(12);
    doc.setFont(undefined, "bold");
    doc.text("EXPERIENCE", 20, mainY);
    mainY += 8;
    
    resume.experience.forEach((exp) => {
      if (mainY > pageHeight - 30) {
        doc.addPage();
        mainY = 20;
      }
      
      doc.setFontSize(11);
      doc.setFont(undefined, "bold");
      doc.text(exp.title, 20, mainY);
      mainY += 5;
      
      doc.setFontSize(9);
      doc.setFont(undefined, "italic");
      doc.text(`${exp.company} • ${exp.duration}`, 20, mainY);
      mainY += 5;
      
      doc.setFontSize(9);
      doc.setFont(undefined, "normal");
      const descLines = doc.splitTextToSize(exp.description, mainContentWidth - 20);
      doc.text(descLines, 20, mainY);
      mainY += descLines.length * 4 + 8;
    });
  }
  
  // Education
  if (resume?.education?.length) {
    mainY += 5;
    doc.setFontSize(12);
    doc.setFont(undefined, "bold");
    doc.text("EDUCATION", 20, mainY);
    mainY += 8;
    
    resume.education.forEach((edu) => {
      if (mainY > pageHeight - 30) {
        doc.addPage();
        mainY = 20;
      }
      
      doc.setFontSize(11);
      doc.setFont(undefined, "bold");
      doc.text(edu.degree, 20, mainY);
      mainY += 5;
      
      doc.setFontSize(9);
      doc.setFont(undefined, "italic");
      doc.text(`${edu.school} • ${edu.year}`, 20, mainY);
      mainY += 5;
      
      if (edu.description) {
        doc.setFontSize(9);
        doc.setFont(undefined, "normal");
        const descLines = doc.splitTextToSize(edu.description, mainContentWidth - 20);
        doc.text(descLines, 20, mainY);
        mainY += descLines.length * 4;
      }
      mainY += 8;
    });
  }
}

// Two Column Layout
function generateTwoColumnPDF(
  doc: jsPDF, 
  data: PDFData, 
  template: CVTemplate, 
  primaryColor: { r: number; g: number; b: number },
  secondaryColor: { r: number; g: number; b: number }
): void {
  const { user, resume, credentials } = data;
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  
  // Add watermark for free users
  const isPro = user.plan === "pro" || user.plan === "agency";
  if (!isPro) {
    doc.setTextColor(200, 200, 200);
    doc.setFontSize(50);
    doc.text("JUANCV FREE", pageWidth / 2, pageHeight / 2, { align: "center", angle: 45 });
    doc.setTextColor(0, 0, 0);
  }

  // Header background
  doc.setFillColor(primaryColor.r, primaryColor.g, primaryColor.b);
  doc.rect(0, 0, pageWidth, 40, "F");
  
  let yPos = 20;
  const columnWidth = (pageWidth - 40) / 2;
  const leftColumnX = 20;
  const rightColumnX = leftColumnX + columnWidth + 10;
  
  // Header with photo
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(20);
  doc.setFont(undefined, "bold");
  const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Professional';
  doc.text(fullName, leftColumnX, yPos);
  
  if (resume?.title) {
    doc.setFontSize(12);
    doc.text(resume.title, leftColumnX, yPos + 8);
  }
  
  // Profile photo in header
  const photoSrc = resume?.profilePhotoUrl || user?.profileImageUrl;
  if (photoSrc) {
    try {
      const photoSize = 30;
      const photoX = pageWidth - photoSize - 20;
      doc.addImage(photoSrc, "JPEG", photoX, 5, photoSize, photoSize);
    } catch (error) {
      console.warn("Failed to add profile photo:", error);
    }
  }
  
  yPos = 55;
  doc.setTextColor(0, 0, 0);
  
  // Left column
  let leftY = yPos;
  
  // Contact info
  doc.setFontSize(10);
  doc.setFont(undefined, "bold");
  doc.text("CONTACT", leftColumnX, leftY);
  leftY += 8;
  
  doc.setFontSize(9);
  doc.setFont(undefined, "normal");
  const contactInfo = [user.email, resume?.phone, resume?.location, resume?.website].filter(Boolean);
  contactInfo.forEach(info => {
    doc.text(info, leftColumnX, leftY);
    leftY += 5;
  });
  
  // Skills
  if (resume?.skills?.length) {
    leftY += 5;
    doc.setFontSize(10);
    doc.setFont(undefined, "bold");
    doc.text("SKILLS", leftColumnX, leftY);
    leftY += 8;
    
    doc.setFontSize(9);
    doc.setFont(undefined, "normal");
    resume.skills.forEach(skill => {
      doc.text(skill, leftColumnX, leftY);
      leftY += 5;
    });
  }
  
  // Right column
  let rightY = yPos;
  
  // Summary
  if (resume?.summary) {
    doc.setFontSize(10);
    doc.setFont(undefined, "bold");
    doc.text("SUMMARY", rightColumnX, rightY);
    rightY += 8;
    
    doc.setFontSize(9);
    doc.setFont(undefined, "normal");
    const summaryLines = doc.splitTextToSize(resume.summary, columnWidth);
    doc.text(summaryLines, rightColumnX, rightY);
    rightY += summaryLines.length * 4 + 10;
  }
  
  // Experience
  if (resume?.experience?.length) {
    doc.setFontSize(10);
    doc.setFont(undefined, "bold");
    doc.text("EXPERIENCE", rightColumnX, rightY);
    rightY += 8;
    
    resume.experience.forEach((exp) => {
      if (rightY > pageHeight - 30) {
        doc.addPage();
        rightY = 20;
      }
      
      doc.setFontSize(9);
      doc.setFont(undefined, "bold");
      doc.text(exp.title, rightColumnX, rightY);
      rightY += 5;
      
      doc.setFontSize(8);
      doc.setFont(undefined, "italic");
      doc.text(`${exp.company} • ${exp.duration}`, rightColumnX, rightY);
      rightY += 5;
      
      doc.setFontSize(8);
      doc.setFont(undefined, "normal");
      const descLines = doc.splitTextToSize(exp.description, columnWidth);
      doc.text(descLines, rightColumnX, rightY);
      rightY += descLines.length * 3 + 5;
    });
  }
  
  // Education
  if (resume?.education?.length) {
    rightY += 5;
    doc.setFontSize(10);
    doc.setFont(undefined, "bold");
    doc.text("EDUCATION", rightColumnX, rightY);
    rightY += 8;
    
    resume.education.forEach((edu) => {
      if (rightY > pageHeight - 30) {
        doc.addPage();
        rightY = 20;
      }
      
      doc.setFontSize(9);
      doc.setFont(undefined, "bold");
      doc.text(edu.degree, rightColumnX, rightY);
      rightY += 5;
      
      doc.setFontSize(8);
      doc.setFont(undefined, "italic");
      doc.text(`${edu.school} • ${edu.year}`, rightColumnX, rightY);
      rightY += 5;
      
      if (edu.description) {
        doc.setFontSize(8);
        doc.setFont(undefined, "normal");
        const descLines = doc.splitTextToSize(edu.description, columnWidth);
        doc.text(descLines, rightColumnX, rightY);
        rightY += descLines.length * 3;
      }
      rightY += 5;
    });
  }
}
