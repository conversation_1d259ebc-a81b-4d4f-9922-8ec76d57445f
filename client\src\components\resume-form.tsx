import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Plus, X, Loader2 } from "lucide-react";
import { z } from "zod";
import PhotoUpload from "./photo-upload";

const resumeFormSchema = z.object({
  title: z.string()
    .trim()
    .max(100, "Title must be less than 100 characters")
    .refine(val => val === "" || val.length >= 2, "Title must be at least 2 characters when provided")
    .optional()
    .or(z.literal("")),
  summary: z.string()
    .trim()
    .max(500, "Summary must be less than 500 characters")
    .refine(val => val === "" || val.length >= 20, "Summary must be at least 20 characters when provided")
    .optional()
    .or(z.literal("")),
  phone: z.string()
    .trim()
    .max(20, "Phone number must be less than 20 characters")
    .refine(val => val === "" || /^[\+]?[0-9\s\-\(\)]{7,}$/.test(val), "Please enter a valid phone number")
    .optional()
    .or(z.literal("")),
  location: z.string()
    .trim()
    .max(100, "Location must be less than 100 characters")
    .refine(val => val === "" || val.length >= 2, "Location must be at least 2 characters when provided")
    .optional()
    .or(z.literal("")),
  website: z.string()
    .trim()
    .refine(val => val === "" || z.string().url().safeParse(val).success, "Please enter a valid website URL")
    .optional()
    .or(z.literal("")),
  profilePhotoUrl: z.string().optional(),
  skills: z.array(z.string().trim().min(1, "Skill cannot be empty"))
    .max(20, "Maximum 20 skills allowed")
    .optional()
    .default([]),
  experience: z.array(z.object({
    title: z.string()
      .trim()
      .max(100, "Job title must be less than 100 characters")
      .refine(val => val === "" || val.length >= 2, "Job title must be at least 2 characters when provided")
      .optional()
      .or(z.literal("")),
    company: z.string()
      .trim()
      .max(100, "Company name must be less than 100 characters")
      .refine(val => val === "" || val.length >= 2, "Company name must be at least 2 characters when provided")
      .optional()
      .or(z.literal("")),
    duration: z.string()
      .trim()
      .max(50, "Duration must be less than 50 characters")
      .refine(val => val === "" || val.length >= 2, "Duration must be at least 2 characters when provided")
      .optional()
      .or(z.literal("")),
    description: z.string()
      .trim()
      .max(500, "Description must be less than 500 characters")
      .refine(val => val === "" || val.length >= 10, "Description must be at least 10 characters when provided")
      .optional()
      .or(z.literal("")),
  })).optional().default([]),
  education: z.array(z.object({
    degree: z.string()
      .trim()
      .max(100, "Degree must be less than 100 characters")
      .refine(val => val === "" || val.length >= 2, "Degree must be at least 2 characters when provided")
      .optional()
      .or(z.literal("")),
    school: z.string()
      .trim()
      .max(100, "School name must be less than 100 characters")
      .refine(val => val === "" || val.length >= 2, "School name must be at least 2 characters when provided")
      .optional()
      .or(z.literal("")),
    year: z.string()
      .trim()
      .max(20, "Year must be less than 20 characters")
      .refine(val => val === "" || /^\d{4}(-\d{4})?$/.test(val), "Please enter a valid year format (e.g., 2020 or 2020-2024)")
      .optional()
      .or(z.literal("")),
    description: z.string()
      .trim()
      .max(300, "Description must be less than 300 characters")
      .optional()
      .or(z.literal("")),
  })).optional().default([]),
});

type ResumeFormValues = z.infer<typeof resumeFormSchema>;

interface ResumeFormProps {
  initialData?: any;
}

export default function ResumeForm({ initialData }: ResumeFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [newSkill, setNewSkill] = useState("");

  const form = useForm<ResumeFormValues>({
    resolver: zodResolver(resumeFormSchema),
    defaultValues: {
      title: initialData?.title || "",
      summary: initialData?.summary || "",
      phone: initialData?.phone || "",
      location: initialData?.location || "",
      website: initialData?.website || "",
      profilePhotoUrl: initialData?.profilePhotoUrl || "",
      skills: initialData?.skills || [],
      experience: initialData?.experience || [],
      education: initialData?.education || [],
    },
  });

  // Reset form when initialData changes
  useEffect(() => {
    if (initialData) {
      form.reset({
        title: initialData.title || "",
        summary: initialData.summary || "",
        phone: initialData.phone || "",
        location: initialData.location || "",
        website: initialData.website || "",
        profilePhotoUrl: initialData.profilePhotoUrl || "",
        skills: initialData.skills || [],
        experience: initialData.experience || [],
        education: initialData.education || [],
      });
    }
  }, [initialData, form]);

  const mutation = useMutation({
    mutationFn: async (data: ResumeFormValues) => {
      return await apiRequest("/api/resume", { method: "POST", data });
    },
    onSuccess: () => {
      toast({
        title: "Resume Saved",
        description: "Your resume has been saved successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/resume"] });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to save resume. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: ResumeFormValues) => {
    mutation.mutate(data);
  };

  const handlePhotoUploaded = () => {
    // Automatically save the resume when photo is uploaded
    const formData = form.getValues();
    mutation.mutate(formData);
  };

  const addSkill = () => {
    const trimmedSkill = newSkill.trim();
    if (trimmedSkill) {
      const currentSkills = form.getValues("skills") || [];
      
      // Check for duplicates
      if (currentSkills.includes(trimmedSkill)) {
        toast({
          title: "Duplicate skill",
          description: "This skill has already been added",
          variant: "destructive"
        });
        return;
      }
      
      // Check length
      if (trimmedSkill.length < 2) {
        toast({
          title: "Skill too short",
          description: "Skills must be at least 2 characters long",
          variant: "destructive"
        });
        return;
      }
      
      if (trimmedSkill.length > 30) {
        toast({
          title: "Skill too long",
          description: "Skills must be less than 30 characters",
          variant: "destructive"
        });
        return;
      }
      
      form.setValue("skills", [...currentSkills, trimmedSkill]);
      setNewSkill("");
    }
  };

  const removeSkill = (index: number) => {
    const currentSkills = form.getValues("skills") || [];
    form.setValue("skills", currentSkills.filter((_, i) => i !== index));
  };

  const addExperience = () => {
    const currentExperience = form.getValues("experience") || [];
    form.setValue("experience", [
      ...currentExperience,
      { title: "", company: "", duration: "", description: "" },
    ]);
  };

  const removeExperience = (index: number) => {
    const currentExperience = form.getValues("experience") || [];
    form.setValue("experience", currentExperience.filter((_, i) => i !== index));
  };

  const addEducation = () => {
    const currentEducation = form.getValues("education") || [];
    form.setValue("education", [
      ...currentEducation,
      { degree: "", school: "", year: "", description: "" },
    ]);
  };

  const removeEducation = (index: number) => {
    const currentEducation = form.getValues("education") || [];
    form.setValue("education", currentEducation.filter((_, i) => i !== index));
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Resume Builder</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Profile Photo Section */}
            <div className="flex flex-col items-center space-y-4">
              <h3 className="text-lg font-medium">Profile Photo</h3>
              <PhotoUpload
                currentPhoto={form.watch("profilePhotoUrl")}
                onPhotoChange={(url) => form.setValue("profilePhotoUrl", url)}
                onPhotoUploaded={handlePhotoUploaded}
                disabled={mutation.isPending}
              />
            </div>

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Professional Title</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Senior Software Engineer" 
                      {...field}
                      maxLength={100}
                    />
                  </FormControl>
                  <FormMessage />
                  <p className="text-xs text-muted-foreground">
                    {field.value?.length || 0}/100 characters
                  </p>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="summary"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Professional Summary</FormLabel>
                  <FormControl>
                    <Textarea 
                      rows={4} 
                      placeholder="Experienced software engineer with 5+ years in full-stack development..."
                      className="resize-none"
                      {...field}
                      maxLength={500}
                    />
                  </FormControl>
                  <FormMessage />
                  <p className="text-xs text-muted-foreground">
                    {field.value?.length || 0}/500 characters
                    {field.value && field.value.length > 0 && field.value.length < 20 && (
                      <span className="text-amber-600 ml-1">(minimum 20 characters recommended)</span>
                    )}
                  </p>
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="+63 ************" 
                        {...field}
                        maxLength={15}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Manila, Philippines" 
                        {...field}
                        maxLength={100}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Website/Portfolio</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="https://yourportfolio.com" 
                      {...field}
                      type="url"
                    />
                  </FormControl>
                  <FormMessage />
                  <p className="text-xs text-muted-foreground">
                    Optional - Enter a valid URL
                  </p>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Skills */}
        <Card>
          <CardHeader>
            <CardTitle>Skills</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {(form.watch("skills") || []).map((skill, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {skill}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 hover:bg-transparent"
                    onClick={() => removeSkill(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  placeholder="Add a skill"
                  value={newSkill}
                  onChange={(e) => setNewSkill(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
                  maxLength={30}
                />
                <Button 
                  type="button" 
                  onClick={addSkill} 
                  variant="outline"
                  disabled={!newSkill.trim() || (form.watch("skills") || []).length >= 20}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                {newSkill.length}/30 characters • {(form.watch("skills") || []).length}/20 skills
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Experience */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Work Experience</CardTitle>
              <Button type="button" onClick={addExperience} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Experience
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {(form.watch("experience") || []).map((_, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-4">
                <div className="flex justify-between items-start">
                  <h4 className="font-medium">Experience {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeExperience(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name={`experience.${index}.title`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Job Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Senior Software Engineer" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`experience.${index}.company`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Company</FormLabel>
                        <FormControl>
                          <Input placeholder="Tech Solutions Inc." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name={`experience.${index}.duration`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duration</FormLabel>
                      <FormControl>
                        <Input placeholder="2021 - Present" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`experience.${index}.description`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          rows={3}
                          placeholder="Led development of microservices architecture..."
                          className="resize-none"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Education */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Education</CardTitle>
              <Button type="button" onClick={addEducation} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Education
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {(form.watch("education") || []).map((_, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-4">
                <div className="flex justify-between items-start">
                  <h4 className="font-medium">Education {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeEducation(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name={`education.${index}.degree`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Degree</FormLabel>
                        <FormControl>
                          <Input placeholder="Bachelor of Science in Computer Science" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`education.${index}.school`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>School</FormLabel>
                        <FormControl>
                          <Input placeholder="University of the Philippines" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name={`education.${index}.year`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Year</FormLabel>
                      <FormControl>
                        <Input placeholder="2018" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`education.${index}.description`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Textarea 
                          rows={2}
                          placeholder="Magna Cum Laude, Dean's List..."
                          className="resize-none"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Save Changes Button - Positioned at the end of all form sections */}
        <div className="pt-8 border-t border-gray-200">
          <div className="flex justify-center">
            <Button 
              type="submit" 
              disabled={mutation.isPending}
              className="w-full sm:w-auto sm:min-w-[200px] bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-3 px-8 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
            >
              {mutation.isPending && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              {mutation.isPending ? "Saving Changes..." : "Save Changes"}
            </Button>
          </div>
          <p className="text-sm text-gray-500 text-center mt-3">
            Your changes will be saved automatically and reflected in your PDF downloads
          </p>
        </div>
      </form>
    </Form>
  );
}
