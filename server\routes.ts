import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { setupAuth, isAuthenticated } from "./replitAuth";
import { insertResumeSchema, insertCredentialSchema } from "@shared/schema";
import multer from "multer";
import axios from "axios";
// PDF generation will be handled by frontend using jsPDF

// Helper function to get client IP
function getClientIp(req: any): string {
  return req.headers['x-forwarded-for']?.split(',')[0] || 
         req.headers['x-real-ip'] || 
         req.connection?.remoteAddress || 
         req.socket?.remoteAddress || 
         '127.0.0.1';
}

// Maya Checkout API configuration
const MAYA_PUBLIC_KEY = process.env.MAYA_PUBLIC_KEY;
const MAYA_SECRET_KEY = process.env.MAYA_SECRET_KEY;

if (!MAYA_PUBLIC_KEY || !MAYA_SECRET_KEY) {
  throw new Error("MAYA_PUBLIC_KEY and MAYA_SECRET_KEY are required");
}

// Maya Checkout API helper
const mayaApi = axios.create({
  baseURL: 'https://pg.paymaya.com', // Use production endpoint
  headers: {
    'Content-Type': 'application/json',
  },
});

// File storage will be handled directly in the database using base64 encoding

// Multer setup for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF, JPG, and PNG files are allowed.'));
    }
  }
});

// Photo upload specific setup (smaller file size limit)
const photoUpload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB for photos
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPG and PNG files are allowed.'));
    }
  }
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth middleware
  await setupAuth(app);

  // Auth routes
  app.get('/api/auth/user', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      res.json(user);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Update user profile
  app.put('/api/profile', isAuthenticated, async (req: any, res) => {
    try {
      const { firstName, lastName, email } = req.body;
      const userId = req.user.claims.sub;

      // Validate input
      if (!firstName || !lastName) {
        return res.status(400).json({ message: "First name and last name are required" });
      }

      // Update user profile
      await storage.updateUser(userId, {
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        email: email.trim(),
      });

      // Return updated user data
      const updatedUser = await storage.getUser(userId);
      res.json(updatedUser);
    } catch (error) {
      console.error("Error updating profile:", error);
      res.status(500).json({ message: "Failed to update profile" });
    }
  });

  // Resume routes
  app.get('/api/resume', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const resume = await storage.getResumeByUserId(userId);
      res.json(resume);
    } catch (error) {
      console.error("Error fetching resume:", error);
      res.status(500).json({ message: "Failed to fetch resume" });
    }
  });

  app.post('/api/resume', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      console.log("Resume save request size:", JSON.stringify(req.body).length);
      const resumeData = insertResumeSchema.parse({ ...req.body, userId });
      const resume = await storage.createOrUpdateResume(resumeData);
      res.json(resume);
    } catch (error) {
      console.error("Error saving resume:", error);
      if (error.type === 'entity.too.large') {
        res.status(413).json({ message: "Resume data too large. Please try with a smaller photo." });
      } else {
        res.status(500).json({ message: "Failed to save resume" });
      }
    }
  });

  // Credential routes
  app.get('/api/credentials', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const credentials = await storage.getCredentialsByUserId(userId);
      res.json(credentials);
    } catch (error) {
      console.error("Error fetching credentials:", error);
      res.status(500).json({ message: "Failed to fetch credentials" });
    }
  });

  app.post('/api/credentials/upload', isAuthenticated, upload.single('file'), async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Check upload limits based on plan
      const existingCredentials = await storage.getCredentialsByUserId(userId);
      const planLimits = { free: 3, pro: 20, agency: 100 };
      const limit = planLimits[user.plan as keyof typeof planLimits] || 3;
      
      if (existingCredentials.length >= limit) {
        return res.status(400).json({ message: `Upload limit reached for ${user.plan} plan` });
      }

      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      // Convert file to base64 and store in database
      const base64Data = req.file.buffer.toString('base64');
      const dataUrl = `data:${req.file.mimetype};base64,${base64Data}`;

      const credentialData = insertCredentialSchema.parse({
        userId,
        title: req.body.title || req.file.originalname,
        description: req.body.description || '',
        fileUrl: dataUrl,
        fileName: req.file.originalname,
        fileSize: req.file.size,
        fileType: req.file.mimetype,
        isPublic: req.body.isPublic === 'true',
        issuer: req.body.issuer || '',
        issuedDate: req.body.issuedDate || '',
      });

      const credential = await storage.createCredential(credentialData);
      
      // Track credential upload
      try {
        await storage.trackCredentialUpload({
          userId,
          credentialId: credential.id,
        });
      } catch (trackingError) {
        console.error('Error tracking credential upload:', trackingError);
      }

      res.json(credential);
    } catch (error) {
      console.error("Error uploading credential:", error);
      res.status(500).json({ message: "Failed to upload credential" });
    }
  });

  app.put('/api/credentials/:id', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const credentialId = parseInt(req.params.id);
      const { title, description, issuer, issuedDate, isPublic } = req.body;
      
      // Build updates object with only provided fields
      const updates: any = {};
      if (title !== undefined && title !== null) updates.title = title;
      if (description !== undefined && description !== null) updates.description = description;
      if (issuer !== undefined && issuer !== null) updates.issuer = issuer;
      if (issuedDate !== undefined && issuedDate !== null) updates.issuedDate = issuedDate;
      if (isPublic !== undefined && isPublic !== null) updates.isPublic = isPublic;
      
      // Check if there are any updates to apply
      if (Object.keys(updates).length === 0) {
        return res.status(400).json({ message: "No updates provided" });
      }
      
      const updatedCredential = await storage.updateCredential(credentialId, userId, updates);
      
      if (updatedCredential) {
        res.json(updatedCredential);
      } else {
        res.status(404).json({ message: "Credential not found or access denied" });
      }
    } catch (error) {
      console.error("Error updating credential:", error);
      res.status(500).json({ message: "Failed to update credential" });
    }
  });

  app.delete('/api/credentials/:id', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const credentialId = parseInt(req.params.id);
      const success = await storage.deleteCredential(credentialId, userId);
      
      if (success) {
        res.json({ message: "Credential deleted successfully" });
      } else {
        res.status(404).json({ message: "Credential not found" });
      }
    } catch (error) {
      console.error("Error deleting credential:", error);
      res.status(500).json({ message: "Failed to delete credential" });
    }
  });

  // Route to serve individual credential files
  app.get('/api/credentials/:id/file', async (req: any, res) => {
    try {
      const credentialId = parseInt(req.params.id);
      const credential = await storage.getCredentialById(credentialId);
      
      if (!credential) {
        return res.status(404).json({ message: "Credential not found" });
      }
      
      // Check if credential is public or if user owns it
      const isPublic = credential.isPublic;
      const userId = req.user?.claims?.sub;
      const isOwner = userId && credential.userId === userId;
      
      if (!isPublic && !isOwner) {
        return res.status(403).json({ message: "Access denied" });
      }
      
      // Return the file data
      res.json({
        fileUrl: credential.fileUrl,
        fileName: credential.fileName,
        fileType: credential.fileType,
        title: credential.title
      });
    } catch (error) {
      console.error("Error serving credential file:", error);
      res.status(500).json({ message: "Failed to serve credential file" });
    }
  });

  // Photo upload endpoint
  app.post('/api/upload/photo', isAuthenticated, photoUpload.single('file'), async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      console.log('Photo upload request received for user:', userId);
      console.log('Request file:', req.file ? 'File present' : 'No file');
      console.log('Request body:', req.body);
      
      if (!req.file) {
        console.log('No file in request');
        return res.status(400).json({ message: "No file uploaded" });
      }

      console.log('File details:', {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      });

      // Convert image to base64 and store in database
      const base64Data = req.file.buffer.toString('base64');
      const dataUrl = `data:${req.file.mimetype};base64,${base64Data}`;

      console.log('Photo upload successful for user:', userId);
      res.json({
        url: dataUrl,
        message: "Photo uploaded successfully"
      });
    } catch (error) {
      console.error("Error uploading photo:", error);
      res.status(500).json({ message: "Failed to upload photo" });
    }
  });

  // Public profile routes
  app.get('/api/profile/:username', async (req, res) => {
    try {
      const { username } = req.params;
      const user = await storage.getUserByUsername(username);
      
      if (!user) {
        return res.status(404).json({ message: "Profile not found" });
      }

      const resume = await storage.getResumeByUserId(user.id);
      const credentials = await storage.getPublicCredentialsByUserId(user.id);

      // Track profile view
      try {
        await storage.trackProfileView({
          userId: user.id,
          viewerIp: getClientIp(req),
          userAgent: req.headers['user-agent'] || null,
          referrer: req.headers['referer'] || null,
        });
      } catch (trackingError) {
        console.error('Error tracking profile view:', trackingError);
      }

      res.json({
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          profileImageUrl: user.profileImageUrl,
          username: user.username,
        },
        resume,
        credentials,
      });
    } catch (error) {
      console.error("Error fetching public profile:", error);
      res.status(500).json({ message: "Failed to fetch profile" });
    }
  });

  // Public PDF data endpoint
  app.get('/api/pdf-data/public/:username', async (req, res) => {
    try {
      const { username } = req.params;
      const user = await storage.getUserByUsername(username);
      
      if (!user) {
        return res.status(404).json({ message: "Profile not found" });
      }

      const resume = await storage.getResumeByUserId(user.id);
      const credentials = await storage.getPublicCredentialsByUserId(user.id);

      // Track download event
      try {
        await storage.trackDownload({
          userId: user.id,
          downloadType: 'resume_pdf',
          downloaderIp: getClientIp(req),
          userAgent: req.headers['user-agent'] || null,
        });
      } catch (trackingError) {
        console.error('Error tracking download event:', trackingError);
      }

      const pdfData = {
        user: {
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email,
          profileImageUrl: user.profileImageUrl,
        },
        resume,
        credentials: credentials || [],
      };

      res.json(pdfData);
    } catch (error) {
      console.error("Error fetching public PDF data:", error);
      res.status(500).json({ message: "Failed to fetch PDF data" });
    }
  });

  // Maya Checkout session creation
  app.post('/api/create-checkout-session', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      if (user.plan === 'pro' || user.plan === 'agency') {
        return res.status(400).json({ message: "User already has a premium plan" });
      }

      // Check for existing pending payments (prevent multiple attempts)
      const existingPending = await storage.getUserPendingPayments(userId);
      if (existingPending.length > 0) {
        const pendingPayment = existingPending[0];
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
        
        if (pendingPayment.createdAt > thirtyMinutesAgo) {
          return res.status(400).json({ 
            message: "You already have a pending payment. Please wait or try again later.",
            pendingUntil: new Date(pendingPayment.createdAt.getTime() + 30 * 60 * 1000).toISOString()
          });
        } else {
          // Auto-cleanup expired pending transactions
          await storage.cleanupExpiredPendingPayments(userId);
        }
      }

      // Create Maya Checkout session using Maya's native API
      const checkoutData = {
        totalAmount: {
          value: 99.00,
          currency: 'PHP',
          details: {
            discount: 0,
            serviceCharge: 0,
            shippingFee: 0,
            tax: 0,
            subtotal: 99.00
          }
        },
        buyer: {
          firstName: user.firstName || 'Customer',
          lastName: user.lastName || '',
          contact: {
            phone: '+639000000000', // Default phone
            email: user.email || '<EMAIL>'
          }
        },
        items: [
          {
            name: 'JuanCV Pro Plan',
            quantity: 1,
            code: 'JUANCV-PRO',
            description: 'Monthly subscription to JuanCV Pro Plan',
            amount: {
              value: 99.00,
              currency: 'PHP'
            },
            totalAmount: {
              value: 99.00,
              currency: 'PHP'
            }
          }
        ],
        redirectUrl: {
          success: `${req.protocol}://${req.get('host')}/payment/success?checkout_id={{checkout_id}}`,
          failure: `${req.protocol}://${req.get('host')}/payment/failed?checkout_id={{checkout_id}}`,
          cancel: `${req.protocol}://${req.get('host')}/payment/failed?checkout_id={{checkout_id}}`
        },
        webhookUrl: `${req.protocol}://${req.get('host')}/api/webhook`,
        requestReferenceNumber: `JUANCV-${userId}-${Date.now()}`,
        metadata: {
          user_id: userId,
          plan: 'pro'
        }
      };

      // Maya uses public key for checkout creation
      const auth = Buffer.from(MAYA_PUBLIC_KEY + ':').toString('base64');
      
      console.log('Maya API Key format check:', {
        hasPublicKey: !!MAYA_PUBLIC_KEY,
        keyPrefix: MAYA_PUBLIC_KEY?.substring(0, 5),
        keyLength: MAYA_PUBLIC_KEY?.length,
        authHeader: `Basic ${auth.substring(0, 20)}...`,
        webhookUrl: `${req.protocol}://${req.get('host')}/api/webhook`
      });
      
      const checkoutResponse = await mayaApi.post('/checkout/v1/checkouts', checkoutData, {
        headers: {
          'Authorization': `Basic ${auth}`
        }
      });

      const checkoutSession = checkoutResponse.data;

      // Only create payment record AFTER successful Maya session initialization
      console.log('Maya checkout session created successfully:', {
        checkoutId: checkoutSession.checkoutId,
        userId: userId,
        amount: 9900
      });

      const payment = await storage.createPayment({
        userId,
        mayaCheckoutId: checkoutSession.checkoutId,
        referenceNumber: checkoutData.requestReferenceNumber,
        amount: 9900, // ₱99.00 in centavos
        currency: 'PHP',
        status: 'pending',
      });

      console.log('✅ Payment record created AFTER Maya session initialization:', {
        id: payment.id,
        userId: payment.userId,
        status: payment.status,
        mayaCheckoutId: payment.mayaCheckoutId
      });

      res.json({
        checkout_url: checkoutSession.redirectUrl,
        checkout_id: checkoutSession.checkoutId,
        payment_id: payment.id,
        status: 'session_created'
      });
    } catch (error: any) {
      console.error("Error creating Maya Checkout session:", error);
      console.error("Error details:", error.response?.data);
      res.status(400).json({ 
        error: { 
          message: error.response?.data?.errors?.[0]?.detail || error.message 
        } 
      });
    }
  });

  // User subscription status and payment history
  app.get('/api/user/:userId/subscription', isAuthenticated, async (req: any, res) => {
    try {
      const { userId } = req.params;
      const currentUserId = req.user.claims.sub;
      
      // Users can only access their own subscription data
      if (currentUserId !== userId) {
        return res.status(403).json({ message: "Access denied" });
      }

      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const [payments, latestPayment] = await Promise.all([
        storage.getUserPayments(userId),
        storage.getLatestPayment(userId)
      ]);

      res.json({
        subscription: {
          plan: user.plan,
          isActive: user.plan === 'pro' || user.plan === 'agency',
          expiresAt: user.subscriptionExpiresAt,
        },
        payments: payments.map(payment => ({
          id: payment.id,
          mayaCheckoutId: payment.mayaCheckoutId,
          referenceNumber: payment.referenceNumber,
          amount: payment.amount,
          currency: payment.currency,
          status: payment.status,
          paymentMethod: payment.paymentMethod,
          receiptNumber: payment.receiptNumber,
          paidAt: payment.paidAt,
          createdAt: payment.createdAt,
        })),
        latestPayment: latestPayment ? {
          id: latestPayment.id,
          mayaCheckoutId: latestPayment.mayaCheckoutId,
          referenceNumber: latestPayment.referenceNumber,
          amount: latestPayment.amount,
          currency: latestPayment.currency,
          status: latestPayment.status,
          receiptNumber: latestPayment.receiptNumber,
          paidAt: latestPayment.paidAt,
          createdAt: latestPayment.createdAt,
        } : null
      });
    } catch (error) {
      console.error("Error fetching subscription data:", error);
      res.status(500).json({ message: "Failed to fetch subscription data" });
    }
  });

  // PDF Generation data routes - frontend will handle PDF generation
  app.get('/api/pdf-data/:userId', isAuthenticated, async (req: any, res) => {
    try {
      const { userId } = req.params;
      const currentUserId = req.user.claims.sub;
      
      // Users can only access their own PDF data
      if (currentUserId !== userId) {
        return res.status(403).json({ message: "Access denied" });
      }

      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const resume = await storage.getResumeByUserId(userId);
      const credentials = await storage.getCredentialsByUserId(userId);

      res.json({
        user: {
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email,
          plan: user.plan,
          profileImageUrl: user.profileImageUrl,
        },
        resume: resume ? {
          ...resume,
          profilePhotoUrl: resume.profilePhotoUrl
        } : null,
        credentials,
      });
    } catch (error) {
      console.error("Error fetching PDF data:", error);
      res.status(500).json({ message: "Failed to fetch PDF data" });
    }
  });

  // PDF Generation data for public profiles
  app.get('/api/pdf-data/public/:username', async (req, res) => {
    try {
      const { username } = req.params;
      const user = await storage.getUserByUsername(username);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const resume = await storage.getResumeByUserId(user.id);
      const credentials = await storage.getPublicCredentialsByUserId(user.id);

      // Track download event
      try {
        await storage.trackDownload({
          userId: user.id,
          downloadType: 'resume_pdf',
          downloaderIp: getClientIp(req),
          userAgent: req.headers['user-agent'] || null,
        });
      } catch (trackingError) {
        console.error('Error tracking download:', trackingError);
      }

      res.json({
        user: {
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          plan: user.plan,
        },
        resume: resume ? {
          ...resume,
          profilePhotoUrl: resume.profilePhotoUrl
        } : null,
        credentials,
      });
    } catch (error) {
      console.error("Error fetching public PDF data:", error);
      res.status(500).json({ message: "Failed to fetch PDF data" });
    }
  });

  // Analytics routes
  app.get('/api/analytics/summary', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const summary = await storage.getAnalyticsSummary(userId);
      res.json(summary);
    } catch (error) {
      console.error("Error fetching analytics summary:", error);
      res.status(500).json({ message: "Failed to fetch analytics summary" });
    }
  });

  app.get('/api/analytics/profile-views', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const count = await storage.getProfileViewsCount(userId);
      res.json({ count });
    } catch (error) {
      console.error("Error fetching profile views count:", error);
      res.status(500).json({ message: "Failed to fetch profile views count" });
    }
  });

  app.get('/api/analytics/downloads', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const count = await storage.getDownloadsCount(userId);
      res.json({ count });
    } catch (error) {
      console.error("Error fetching downloads count:", error);
      res.status(500).json({ message: "Failed to fetch downloads count" });
    }
  });

  app.get('/api/analytics/credential-uploads', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const count = await storage.getCredentialUploadsCount(userId);
      res.json({ count });
    } catch (error) {
      console.error("Error fetching credential uploads count:", error);
      res.status(500).json({ message: "Failed to fetch credential uploads count" });
    }
  });

  // Maya webhook for payment notifications with enhanced debugging
  app.post('/api/webhook', async (req, res) => {
    const startTime = Date.now();
    console.log('🚨 WEBHOOK ENDPOINT HIT - CRITICAL DEBUGGING MODE');
    
    try {
      const webhook = req.body;
      const requestInfo = {
        headers: req.headers,
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        method: req.method,
        url: req.url,
        timestamp: new Date().toISOString(),
        contentType: req.headers['content-type'],
        contentLength: req.headers['content-length']
      };
      
      // CRITICAL: Log everything for Maya webhook debugging
      console.log('🔔 MAYA WEBHOOK RECEIVED:', JSON.stringify({
        webhook,
        requestInfo,
        rawBody: req.rawBody || 'Not available',
        bodyType: typeof webhook,
        bodyConstructor: webhook?.constructor?.name,
        isEmpty: Object.keys(webhook || {}).length === 0,
        timestamp: new Date().toISOString()
      }, null, 2));

      // Write to webhook log file for investigation
      const logEntry = {
        timestamp: new Date().toISOString(),
        webhook,
        requestInfo,
        success: false // will be updated if processed successfully
      };
      
      try {
        const fs = await import('fs');
        fs.appendFileSync('webhook-debug.log', JSON.stringify(logEntry) + '\n');
      } catch (logError) {
        console.log('Warning: Could not write to webhook log:', logError.message);
      }

      // Enhanced webhook analysis for debugging
      console.log('🔍 DETAILED WEBHOOK ANALYSIS:', {
        hasId: !!webhook.id,
        hasStatus: !!webhook.status,
        hasIsPaid: !!webhook.isPaid,
        hasCheckoutId: !!webhook.checkoutId,
        hasData: !!webhook.data,
        statusValue: webhook.status,
        isPaidValue: webhook.isPaid,
        idValue: webhook.id,
        checkoutIdValue: webhook.checkoutId,
        allKeys: Object.keys(webhook || {}),
        webhookType: webhook.type || webhook.event_type || 'unknown',
        nestedStructure: {
          hasDataId: !!webhook.data?.id,
          hasDataStatus: !!webhook.data?.status,
          hasDataAttributes: !!webhook.data?.attributes,
          dataKeys: webhook.data ? Object.keys(webhook.data) : []
        }
      });

      // Enhanced extraction for nested Maya webhook formats
      let checkoutId, status, receiptNumber;
      
      // Try multiple checkout ID extraction methods with logging
      if (webhook.id) {
        checkoutId = webhook.id;
        console.log('✅ Found checkout ID in webhook.id:', checkoutId);
      } else if (webhook.checkoutId) {
        checkoutId = webhook.checkoutId;
        console.log('✅ Found checkout ID in webhook.checkoutId:', checkoutId);
      } else if (webhook.data?.id) {
        checkoutId = webhook.data.id;
        console.log('✅ Found checkout ID in webhook.data.id:', checkoutId);
      } else if (webhook.data?.attributes?.id) {
        checkoutId = webhook.data.attributes.id;
        console.log('✅ Found checkout ID in webhook.data.attributes.id:', checkoutId);
      }
      
      if (!checkoutId) {
        console.log('❌ Invalid webhook: missing checkout ID');
        console.log('Tried fields: id, checkoutId, data.id, data.attributes.id');
        console.log('Available fields:', Object.keys(webhook));
        return res.status(400).json({ error: "Webhook missing checkout ID" });
      }

      // Extract status from various possible webhook formats with enhanced detection
      if (webhook.status) {
        status = webhook.status;
        console.log('✅ Found status in webhook.status:', status);
      } else if (webhook.data?.status) {
        status = webhook.data.status;
        console.log('✅ Found status in webhook.data.status:', status);
      } else if (webhook.data?.attributes?.status) {
        status = webhook.data.attributes.status;
        console.log('✅ Found status in webhook.data.attributes.status:', status);
      } else if (webhook.isPaid !== undefined) {
        status = webhook.isPaid ? 'PAID' : 'PENDING';
        console.log('✅ Derived status from webhook.isPaid:', status);
      }
      
      // Extract receipt number from multiple locations
      if (webhook.receiptNumber) {
        receiptNumber = webhook.receiptNumber;
        console.log('✅ Found receipt in webhook.receiptNumber:', receiptNumber);
      } else if (webhook.data?.attributes?.receiptNumber) {
        receiptNumber = webhook.data.attributes.receiptNumber;
        console.log('✅ Found receipt in webhook.data.attributes.receiptNumber:', receiptNumber);
      } else if (webhook.reference) {
        receiptNumber = webhook.reference;
        console.log('✅ Found receipt in webhook.reference:', receiptNumber);
      }
      
      if (!status) {
        console.log('❌ Invalid webhook: missing status');
        console.log('Tried fields: status, data.status, data.attributes.status, isPaid');
        console.log('Available fields:', Object.keys(webhook));
        console.log('Available data fields:', webhook.data ? Object.keys(webhook.data) : []);
        console.log('Available attributes fields:', webhook.data?.attributes ? Object.keys(webhook.data.attributes) : []);
        return res.status(400).json({ error: "Webhook missing status" });
      }

      console.log('🔍 COMPREHENSIVE WEBHOOK EXTRACTION:', {
        extractedCheckoutId: checkoutId,
        extractedStatus: status,
        extractedReceipt: receiptNumber,
        originalWebhook: webhook,
        extractionSuccess: !!checkoutId && !!status
      });

      // Enhanced Maya webhook format support for successful payments
      const isSuccessfulPayment = (
        // Format 1: isPaid flag with COMPLETED status
        (webhook.isPaid && status === 'COMPLETED') ||
        // Format 2: Direct PAID status
        (status === 'PAID') ||
        // Format 3: SUCCESS status
        (status === 'SUCCESS') ||
        // Format 4: COMPLETED status without explicit isPaid=false
        (status === 'COMPLETED' && webhook.isPaid !== false) ||
        // Format 5: Nested data structure
        (webhook.data?.attributes?.status === 'PAID') ||
        (webhook.data?.attributes?.status === 'COMPLETED') ||
        (webhook.data?.attributes?.status === 'SUCCESS') ||
        // Format 6: Payment confirmation events
        (webhook.type === 'payment.paid' || webhook.event_type === 'payment.paid') ||
        // Format 7: Case insensitive variations (CRITICAL FOR MAYA)
        (status?.toLowerCase() === 'paid') ||
        (status?.toLowerCase() === 'completed') ||
        (status?.toLowerCase() === 'success') ||
        // CRITICAL FIX: Maya sends lowercase "success" status
        (status === 'success') ||
        (status?.toUpperCase() === 'PAID') ||
        (status?.toUpperCase() === 'COMPLETED') ||
        (status?.toUpperCase() === 'SUCCESS') ||
        // Format 8: Maya specific status values
        (status === 'payment_paid') ||
        (status === 'payment.paid') ||
        (status === 'checkout.paid') ||
        // Format 9: Nested success indicators
        (webhook.data?.status?.toLowerCase() === 'success') ||
        (webhook.data?.attributes?.status?.toLowerCase() === 'success')
      );

      console.log('🔍 Payment success check:', {
        isPaid: webhook.isPaid,
        status: webhook.status,
        isSuccessfulPayment,
        conditions: {
          'isPaid && COMPLETED': webhook.isPaid && webhook.status === 'COMPLETED',
          'status === PAID': webhook.status === 'PAID',
          'status === SUCCESS': webhook.status === 'SUCCESS',
          'COMPLETED && !isPaid-false': webhook.status === 'COMPLETED' && webhook.isPaid !== false
        }
      });

      if (isSuccessfulPayment) {
        console.log('✅ PAYMENT DETERMINED AS SUCCESSFUL - PROCESSING UPGRADE');
        
        // Extract receipt number with enhanced detection
        if (!receiptNumber) {
          if (webhook.receiptNumber) {
            receiptNumber = webhook.receiptNumber;
          } else if (webhook.data?.attributes?.receiptNumber) {
            receiptNumber = webhook.data.attributes.receiptNumber;
          } else if (webhook.reference) {
            receiptNumber = webhook.reference;
          } else {
            receiptNumber = `AUTO-${Date.now()}`;
          }
        }
        
        // Find the payment record and verify it belongs to our system
        const payment = await storage.getPaymentByCheckoutId(checkoutId);
        
        if (!payment) {
          console.log(`❌ Payment not found for checkout ID: ${checkoutId}`);
          // Log all existing payments for debugging
          const allPayments = await storage.getAllPayments();
          console.log(`📋 Available payment records (${allPayments.length} total):`);
          allPayments.slice(0, 5).forEach(p => {
            console.log(`  - Checkout ID: ${p.mayaCheckoutId}, User: ${p.userId}, Status: ${p.status}`);
          });
          return res.status(404).json({ error: "Payment not found" });
        }

        console.log(`✅ Payment found:`, {
          id: payment.id,
          userId: payment.userId,
          status: payment.status,
          amount: payment.amount,
          createdAt: payment.createdAt
        });

        // Verify payment is still pending (prevent double processing)
        if (payment.status === 'paid') {
          console.log(`Payment already processed: ${checkoutId}`);
          return res.json({ received: true, status: "already_processed" });
        }

        // Verify the payment amount matches what we expect
        const expectedAmount = 9900; // ₱99.00 in centavos
        if (webhook.totalAmount && Math.abs(webhook.totalAmount.value * 100 - expectedAmount) > 1) {
          console.log(`Payment amount mismatch: expected ${expectedAmount}, got ${webhook.totalAmount.value * 100}`);
          return res.status(400).json({ error: "Payment amount mismatch" });
        }
        
        console.log(`🔄 Processing payment upgrade for user ${payment.userId}...`);
        
        // Update payment status to paid FIRST
        const updatedPayment = await storage.updatePaymentStatus(checkoutId, 'paid', receiptNumber);
        console.log(`💳 Payment status updated:`, {
          id: updatedPayment.id,
          status: updatedPayment.status,
          receiptNumber: updatedPayment.receiptNumber,
          paidAt: updatedPayment.paidAt
        });
        
        // Upgrade user subscription AFTER payment is verified
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + 30);
        
        console.log(`⬆️ Upgrading user to Pro plan with expiry: ${expiryDate.toISOString()}`);
        const upgradedUser = await storage.updateUserSubscription(payment.userId, 'pro', expiryDate);
        
        console.log(`✅ USER UPGRADE COMPLETE:`, {
          userId: upgradedUser.id,
          oldPlan: 'free',
          newPlan: upgradedUser.plan,
          subscriptionExpiresAt: upgradedUser.subscriptionExpiresAt,
          checkoutId: checkoutId,
          receiptNumber: receiptNumber,
          amount: `₱${(webhook.totalAmount?.value || 99).toFixed(2)}`
        });
        
        // Verify the upgrade actually worked by fetching fresh user data
        const verificationUser = await storage.getUser(payment.userId);
        if (verificationUser?.plan !== 'pro') {
          console.error(`❌ UPGRADE VERIFICATION FAILED: User ${payment.userId} plan is still ${verificationUser?.plan}, expected 'pro'`);
          
          // Critical: If verification fails, attempt recovery by retrying the upgrade
          console.log(`🔄 ATTEMPTING UPGRADE RECOVERY for user ${payment.userId}...`);
          try {
            const recoveryUser = await storage.updateUserSubscription(payment.userId, 'pro', expiryDate);
            const finalVerificationUser = await storage.getUser(payment.userId);
            
            if (finalVerificationUser?.plan === 'pro') {
              console.log(`✅ UPGRADE RECOVERY SUCCESSFUL: User ${payment.userId} is now on Pro plan`);
            } else {
              throw new Error(`Critical: User upgrade recovery failed - plan is ${finalVerificationUser?.plan} instead of pro`);
            }
          } catch (recoveryError) {
            console.error(`❌ UPGRADE RECOVERY FAILED:`, recoveryError);
            throw new Error(`User upgrade failed and recovery failed - plan is ${verificationUser?.plan} instead of pro`);
          }
        }
        
        console.log(`✅ UPGRADE VERIFICATION PASSED: User ${payment.userId} is now on ${verificationUser?.plan || 'unknown'} plan`);
        console.log(`⏱️ Webhook processing completed in ${Date.now() - startTime}ms`);
        
        // Update log entry as successful
        logEntry.success = true;
        logEntry.userId = payment.userId;
        logEntry.upgradeResult = 'success';
        
        try {
          const fs = await import('fs');
          fs.appendFileSync('webhook-success.log', JSON.stringify({
            ...logEntry,
            completedAt: new Date().toISOString(),
            processingTimeMs: Date.now() - startTime
          }) + '\n');
        } catch (logError) {
          console.log('Warning: Could not write to success log:', logError.message);
        }
        
        return res.json({ 
          received: true, 
          status: "processed", 
          user_upgraded: true,
          userId: payment.userId,
          newPlan: 'pro',
          expiresAt: expiryDate.toISOString(),
          verifiedPlan: verificationUser?.plan || 'unknown',
          processingTimeMs: Date.now() - startTime
        });
      } else if (webhook.status === 'FAILED' || webhook.status === 'CANCELLED') {
        const payment = await storage.getPaymentByCheckoutId(checkoutId);
        
        if (payment) {
          await storage.updatePaymentStatus(checkoutId, status.toLowerCase() as any);
          console.log(`❌ Payment ${status.toLowerCase()}: ${checkoutId}`);
        }
        
        console.log(`⏱️ Webhook processing completed (failed) in ${Date.now() - startTime}ms`);
        return res.json({ received: true, status: status.toLowerCase(), processingTimeMs: Date.now() - startTime });
      } else {
        console.log(`⚠️  Unhandled webhook status: ${status} for checkout: ${checkoutId}`);
        console.log(`⏱️ Webhook processing completed (unhandled) in ${Date.now() - startTime}ms`);
        return res.json({ received: true, status: "unhandled", processingTimeMs: Date.now() - startTime });
      }
    } catch (error) {
      console.error("❌ Maya webhook error:", error);
      console.error("Error details:", {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        processingTimeMs: Date.now() - startTime
      });
      res.status(500).json({ 
        error: "Webhook processing failed", 
        message: error instanceof Error ? error.message : String(error),
        processingTimeMs: Date.now() - startTime
      });
    }
  });



  // DISABLED: Manual payment completion endpoint - SECURITY FIX
  // This endpoint was causing unauthorized upgrades and has been disabled
  app.post('/api/complete-payment', isAuthenticated, async (req: any, res) => {
    const userId = req.user.claims.sub;
    console.log(`🚫 BLOCKED: Manual payment completion attempt by user ${userId} - endpoint disabled for security`);
    return res.status(403).json({ 
      error: "Manual payment completion is disabled for security. Payments are only processed through verified Maya webhooks.",
      message: "Your subscription will be upgraded automatically once Maya confirms your payment."
    });
  });

  // Subscription status endpoint
  app.get('/api/user/:userId/subscription-status', isAuthenticated, async (req: any, res) => {
    try {
      const { userId } = req.params;
      const currentUserId = req.user.claims.sub;
      
      // Users can only access their own subscription data
      if (currentUserId !== userId) {
        return res.status(403).json({ message: "Access denied" });
      }

      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const subscriptionStatus = await storage.checkUserSubscriptionStatus(userId);
      const latestPayment = await storage.getLatestPayment(userId);

      res.json({
        userId,
        plan: user.plan,
        subscriptionExpiresAt: user.subscriptionExpiresAt,
        isExpired: subscriptionStatus.isExpired,
        daysUntilExpiry: subscriptionStatus.daysUntilExpiry,
        status: subscriptionStatus.isExpired ? 'expired' : 'active',
        latestPayment: latestPayment ? {
          id: latestPayment.id,
          status: latestPayment.status,
          amount: latestPayment.amount,
          createdAt: latestPayment.createdAt,
          paidAt: latestPayment.paidAt
        } : null
      });
    } catch (error) {
      console.error("Error fetching subscription status:", error);
      res.status(500).json({ message: "Failed to fetch subscription status" });
    }
  });

  // All Maya test and debugging endpoints have been removed for cleaner codebase

  // Check specific user's subscription status
  app.get('/api/user/:userId/subscription-status', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.params.userId;
      const requestingUserId = req.user.claims.sub;
      
      // Only allow users to check their own status (or admin)
      if (userId !== requestingUserId && requestingUserId !== 'admin') {
        return res.status(403).json({ message: 'Unauthorized' });
      }
      
      const status = await storage.checkUserSubscriptionStatus(userId);
      const user = await storage.getUser(userId);
      
      res.json({
        userId,
        plan: user?.plan || 'free',
        subscriptionExpiresAt: user?.subscriptionExpiresAt,
        isExpired: status.isExpired,
        daysUntilExpiry: status.daysUntilExpiry,
        status: status.isExpired ? 'expired' : status.daysUntilExpiry && status.daysUntilExpiry <= 7 ? 'expiring_soon' : 'active'
      });
    } catch (error) {
      console.error('❌ Error checking subscription status:', error);
      res.status(500).json({ error: 'Failed to check subscription status' });
    }
  });

  // Admin middleware for role checking
  const isAdmin = async (req: any, res: any, next: any) => {
    try {
      const userId = req.user?.claims?.sub;
      if (!userId) {
        return res.status(401).json({ 
          error: "Authentication required", 
          message: "Please log in to access this resource" 
        });
      }

      // Check if user has admin role in database
      const user = await storage.getUser(userId);
      if (!user || user.userType !== 'admin') {
        return res.status(403).json({ 
          error: "Access denied", 
          message: "This endpoint requires admin privileges" 
        });
      }
      
      next();
    } catch (error) {
      console.error("Error in admin middleware:", error);
      res.status(500).json({ 
        error: "Internal server error", 
        message: "Failed to verify admin permissions" 
      });
    }
  };

  // Admin routes for manual billing management
  app.get('/api/admin/users-billing', isAuthenticated, isAdmin, async (req: any, res) => {
    try {
      const usersWithBilling = await storage.getAllUsersWithBillingInfo();
      res.json(usersWithBilling);
    } catch (error) {
      console.error("Error fetching users billing info:", error);
      res.status(500).json({ error: "Failed to fetch users billing information" });
    }
  });

  app.post('/api/admin/update-billing', isAuthenticated, isAdmin, async (req: any, res) => {
    try {
      const adminId = req.user.claims.sub;
      const { userId, newStatus, newExpiryDate, transactionReference, reason } = req.body;
      
      console.log("Admin billing update request body:", req.body);
      console.log("Extracted fields:", { userId, newStatus, reason });
      
      if (!userId || !newStatus || !reason) {
        console.log("Missing fields validation failed:", { userId: !!userId, newStatus: !!newStatus, reason: !!reason });
        return res.status(400).json({ 
          error: "Missing required fields: userId, newStatus, reason",
          received: { userId, newStatus, reason }
        });
      }

      const expiryDate = newExpiryDate ? new Date(newExpiryDate) : undefined;
      
      const result = await storage.updateUserBillingManually(
        userId,
        newStatus,
        reason,
        expiryDate,
        transactionReference
      );

      res.json({
        success: true,
        message: `User ${userId} billing updated successfully`,
        user: result.user,
        logId: result.log.id
      });
    } catch (error) {
      console.error("Error updating user billing:", error);
      res.status(500).json({ 
        error: "Failed to update user billing",
        message: error instanceof Error ? error.message : String(error)
      });
    }
  });

  app.post('/api/admin/cleanup-transactions', isAuthenticated, isAdmin, async (req: any, res) => {
    try {
      const adminId = req.user.claims.sub;
      const { userId, reason } = req.body;
      
      if (!userId || !reason) {
        return res.status(400).json({ 
          error: "Missing required fields: userId, reason" 
        });
      }

      const result = await storage.cleanupRedundantTransactions(userId, reason);

      res.json({
        success: true,
        message: `Cleaned up ${result.removedCount} redundant transactions for user ${userId}`,
        removedCount: result.removedCount,
        logId: result.log.id
      });
    } catch (error) {
      console.error("Error cleaning up transactions:", error);
      res.status(500).json({ 
        error: "Failed to cleanup transactions",
        message: error instanceof Error ? error.message : String(error)
      });
    }
  });

  app.get('/api/admin/billing-logs', isAuthenticated, isAdmin, async (req: any, res) => {
    try {
      const limit = parseInt(req.query.limit as string) || 50;
      const logs = await storage.getManualBillingLogs(limit);
      res.json(logs);
    } catch (error) {
      console.error("Error fetching billing logs:", error);
      res.status(500).json({ error: "Failed to fetch billing logs" });
    }
  });

  app.get('/api/admin/pending-transactions', isAuthenticated, isAdmin, async (req: any, res) => {
    try {
      // Auto-cleanup expired payments during admin view
      const expiredCount = await storage.cleanupAllExpiredPendingPayments();
      
      if (expiredCount > 0) {
        console.log(`🧹 Admin view: Auto-cleaned ${expiredCount} expired pending payments`);
      }
      
      // Get fresh list after cleanup
      const pendingTransactions = await storage.getAllPendingPayments();
      res.json(pendingTransactions);
    } catch (error) {
      console.error("Error fetching pending transactions:", error);
      res.status(500).json({ error: "Failed to fetch pending transactions" });
    }
  });

  // Webhook monitoring endpoints for Maya payment debugging
  app.get('/api/admin/webhook-logs', isAuthenticated, isAdmin, async (req: any, res) => {
    try {
      const fs = await import('fs');
      const logs = [];
      
      // Read debug logs
      try {
        const debugLogs = fs.readFileSync('webhook-debug.log', 'utf8')
          .split('\n')
          .filter(line => line.trim())
          .map(line => JSON.parse(line))
          .slice(-50); // Last 50 entries
        logs.push(...debugLogs);
      } catch (e) {
        // No debug logs yet
      }
      
      // Read success logs
      try {
        const successLogs = fs.readFileSync('webhook-success.log', 'utf8')
          .split('\n')
          .filter(line => line.trim())
          .map(line => JSON.parse(line))
          .slice(-50); // Last 50 entries
        logs.push(...successLogs);
      } catch (e) {
        // No success logs yet
      }
      
      // Sort by timestamp
      logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      res.json({ logs: logs.slice(0, 100) }); // Return last 100 entries
    } catch (error) {
      console.error("Error fetching webhook logs:", error);
      res.status(500).json({ error: "Failed to fetch webhook logs" });
    }
  });

  app.post('/api/admin/webhook-logs/clear', isAuthenticated, isAdmin, async (req: any, res) => {
    try {
      const fs = await import('fs');
      fs.writeFileSync('webhook-debug.log', '');
      fs.writeFileSync('webhook-success.log', '');
      res.json({ message: "Webhook logs cleared" });
    } catch (error) {
      console.error("Error clearing webhook logs:", error);
      res.status(500).json({ error: "Failed to clear webhook logs" });
    }
  });

  // Endpoint to set admin status (should be protected and used carefully)
  app.post('/api/admin/set-user-type', isAuthenticated, isAdmin, async (req: any, res) => {
    try {
      const { userId, userType } = req.body;
      const adminId = req.user.claims.sub;
      
      if (!userId || !userType) {
        return res.status(400).json({ 
          error: "Missing required fields: userId, userType" 
        });
      }

      if (!['user', 'admin'].includes(userType)) {
        return res.status(400).json({ 
          error: "Invalid userType. Must be 'user' or 'admin'" 
        });
      }

      const updatedUser = await storage.updateUserType(userId, userType);
      
      console.log(`👨‍💼 ADMIN USER TYPE UPDATE: Admin ${adminId} changed user ${userId} to ${userType}`);
      
      res.json({
        success: true,
        message: `User ${userId} type updated to ${userType}`,
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          userType: updatedUser.userType
        }
      });
    } catch (error) {
      console.error("Error updating user type:", error);
      res.status(500).json({ 
        error: "Failed to update user type",
        message: error instanceof Error ? error.message : String(error)
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
