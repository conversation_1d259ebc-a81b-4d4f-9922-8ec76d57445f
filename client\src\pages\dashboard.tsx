import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { isUnauthorizedError } from "@/lib/authUtils";
import { generatePDF } from "@/lib/pdfGenerator";
import { CV_TEMPLATES, CVTemplate, DEFAULT_TEMPLATE, TEMPLATE_STORAGE_KEY } from "@/lib/cvTemplates";
import Navbar from "@/components/navbar";
import ResumeForm from "@/components/resume-form";
import CredentialUploader from "@/components/credential-uploader";
import UpgradeBanner from "@/components/upgrade-banner";
import ShareProfile from "@/components/share-profile";
import TemplateSelector from "@/components/template-selector";
import ProfileForm from "@/components/profile-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Eye, 
  Download, 
  Tag, 
  Crown,
  ExternalLink,
  QrCode,
  Copy,
  Share,
  Share2,
  Palette
} from "lucide-react";

export default function Dashboard() {
  const { toast } = useToast();
  const { isAuthenticated, isLoading, user } = useAuth();
  const [showShareModal, setShowShareModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<CVTemplate>(DEFAULT_TEMPLATE);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  // Load saved template from localStorage
  useEffect(() => {
    const savedTemplateId = localStorage.getItem(TEMPLATE_STORAGE_KEY);
    if (savedTemplateId) {
      const template = CV_TEMPLATES.find(t => t.id === savedTemplateId);
      if (template) {
        setSelectedTemplate(template);
      }
    }
  }, []);

  const { data: resume } = useQuery({
    queryKey: ["/api/resume"],
    enabled: isAuthenticated,
    staleTime: 1000 * 60 * 2, // 2 minutes
    refetchOnMount: true, // Always fetch fresh data when returning to dashboard
  });

  const { data: credentials = [] } = useQuery({
    queryKey: ["/api/credentials"],
    enabled: isAuthenticated,
    staleTime: 1000 * 60 * 2, // 2 minutes
    refetchOnMount: true, // Always fetch fresh data when returning to dashboard
  });

  // Ensure credentials is always an array
  const safeCredentials = Array.isArray(credentials) ? credentials : [];

  const { data: analytics } = useQuery({
    queryKey: ["/api/analytics/summary"],
    enabled: isAuthenticated,
    staleTime: 1000 * 60 * 2, // 2 minutes
    refetchOnMount: true, // Always fetch fresh data when returning to dashboard
  });

  // Ensure analytics has safe defaults
  const safeAnalytics = {
    profileViews: analytics?.profileViews || 0,
    downloads: analytics?.downloads || 0,
    credentialUploads: analytics?.credentialUploads || 0,
    recentViews: Array.isArray(analytics?.recentViews) ? analytics.recentViews : [],
    recentDownloads: Array.isArray(analytics?.recentDownloads) ? analytics.recentDownloads : []
  };

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
    }
  }, [isAuthenticated, isLoading, toast]);

  if (isLoading || !isAuthenticated) {
    return <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin w-8 h-8 border-4 border-yellow-500 border-t-transparent rounded-full" />
    </div>;
  }

  const handleCopyLink = () => {
    const profileUrl = `${window.location.origin}/profile/${user?.username || 'username'}`;
    navigator.clipboard.writeText(profileUrl);
    toast({
      title: "Link Copied",
      description: "Profile link copied to clipboard!",
    });
  };

  const handleDownloadPDF = async () => {
    if (!user?.id) return;
    
    try {
      setIsGeneratingPDF(true);
      toast({
        title: "Download Started",
        description: "Your resume PDF is being generated...",
      });

      const response = await fetch(`/api/pdf-data/${user.id}`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch PDF data');
      }

      const pdfData = await response.json();
      generatePDF(pdfData, selectedTemplate.id);

      toast({
        title: "PDF Downloaded",
        description: "Your resume has been downloaded successfully!",
      });
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast({
        title: "Download Failed",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleGeneratePDF = async (templateId: string) => {
    if (!user?.id) return;
    
    try {
      setIsGeneratingPDF(true);
      toast({
        title: "Generating PDF",
        description: "Your resume PDF is being generated with the selected template...",
      });

      const response = await fetch(`/api/pdf-data/${user.id}`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch PDF data');
      }

      const pdfData = await response.json();
      generatePDF(pdfData, templateId);

      toast({
        title: "PDF Generated",
        description: "Your resume has been generated successfully!",
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const userName = user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : 'User';
  const planLimits = { free: 3, pro: 20, agency: 100 };
  const currentLimit = planLimits[user?.plan as keyof typeof planLimits] || 3;

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Welcome back, {userName}!</h1>
            <p className="text-gray-600">Manage your resume and credentials</p>
          </div>
          <div className="flex space-x-3">
            <Button onClick={handleDownloadPDF} className="bg-yellow-500 hover:bg-yellow-600">
              <Download className="w-4 h-4 mr-2" />
              Download PDF
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setShowShareModal(true)}
              disabled={!user?.username}
            >
              <Share2 className="w-4 h-4 mr-2" />
              Share Profile
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200 hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-600 text-sm font-medium">Profile Views</p>
                  <p className="text-2xl font-bold text-yellow-700">
                    {safeAnalytics.profileViews}
                  </p>
                  <p className="text-xs text-yellow-500 mt-1">
                    {safeAnalytics.recentViews.length} recent
                  </p>
                </div>
                <Eye className="text-yellow-500 w-6 h-6" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-pink-50 to-pink-100 border-pink-200 hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-pink-600 text-sm font-medium">Downloads</p>
                  <p className="text-2xl font-bold text-pink-700">
                    {safeAnalytics.downloads}
                  </p>
                  <p className="text-xs text-pink-500 mt-1">
                    {safeAnalytics.recentDownloads.length} recent
                  </p>
                </div>
                <Download className="text-pink-500 w-6 h-6" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200 hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-600 text-sm font-medium">Credentials</p>
                  <p className="text-2xl font-bold text-green-700">{safeCredentials.length}/{currentLimit}</p>
                  <p className="text-xs text-green-500 mt-1">
                    {safeAnalytics.credentialUploads} uploaded
                  </p>
                </div>
                <Tag className="text-green-500 w-6 h-6" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-600 text-sm font-medium">Plan</p>
                  <p className="text-xl font-bold text-blue-700 capitalize">{user?.plan || 'Free'}</p>
                  <p className="text-xs text-blue-500 mt-1">
                    {user?.plan === 'free' ? 'Basic features' : 'Premium features'}
                  </p>
                </div>
                <Crown className="text-blue-500 w-6 h-6" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Upgrade Banner for Free Users */}
        {user?.plan === 'free' && <UpgradeBanner />}

        {/* Main Content Tabs */}
        <Tabs defaultValue="resume" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="resume">Resume Builder</TabsTrigger>
            <TabsTrigger value="credentials">Credentials</TabsTrigger>
            <TabsTrigger value="templates">
              <Palette className="w-4 h-4 mr-2" />
              Templates
            </TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="resume" className="mt-6">
            <ResumeForm initialData={resume} />
          </TabsContent>

          <TabsContent value="credentials" className="mt-6">
            <CredentialUploader 
              credentials={safeCredentials} 
              currentLimit={currentLimit}
              userPlan={user?.plan || 'free'}
            />
          </TabsContent>

          <TabsContent value="templates" className="mt-6">
            <TemplateSelector
              selectedTemplate={selectedTemplate}
              onTemplateSelect={setSelectedTemplate}
              onGeneratePDF={handleGeneratePDF}
              isGenerating={isGeneratingPDF}
            />
          </TabsContent>

          <TabsContent value="profile" className="mt-6">
            <div className="space-y-6">
              {/* Profile Form */}
              {user && <ProfileForm user={user} />}

              {/* Public Profile Preview */}
              <Card>
                <CardHeader>
                  <CardTitle>Public Profile Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="w-16 h-16 bg-gray-300 rounded-full overflow-hidden">
                        {user?.profileImageUrl ? (
                          <img 
                            src={user.profileImageUrl} 
                            alt="Profile" 
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-300 flex items-center justify-center text-gray-600 text-lg font-semibold">
                            {userName.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{userName}</h4>
                        <p className="text-gray-600">{resume?.title || 'Professional Title'}</p>
                        <p className="text-sm text-yellow-500">
                          juancv.app/{user?.username || 'username'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex space-x-3">
                      <Button 
                        className="flex-1 bg-yellow-500 hover:bg-yellow-600"
                        onClick={() => window.open(`/profile/${user?.username || 'username'}`, '_blank')}
                        disabled={!user?.username}
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View Profile
                      </Button>
                      <Button 
                        variant="outline" 
                        className="flex-1"
                        onClick={() => setShowShareModal(true)}
                        disabled={!user?.username}
                      >
                        <QrCode className="w-4 h-4 mr-2" />
                        QR Code
                      </Button>
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-sm text-gray-500 mb-2">Share your profile:</p>
                    <div className="flex">
                      <Input 
                        value={`https://juancv.app/${user?.username || 'username'}`}
                        readOnly 
                        className="flex-1 rounded-r-none bg-gray-50"
                      />
                      <Button 
                        onClick={handleCopyLink}
                        className="rounded-l-none bg-yellow-500 hover:bg-yellow-600"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Recent Activity Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
          {/* Recent Profile Views */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Eye className="w-5 h-5 text-yellow-500" />
                  Recent Profile Views
                </div>
                <Button variant="outline" size="sm" onClick={() => window.location.href = '/analytics'}>
                  View All
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {safeAnalytics.recentViews.length ? (
                  safeAnalytics.recentViews.slice(0, 5).map((view) => (
                    <div key={view.id} className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                          <Eye className="w-4 h-4 text-yellow-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {view.viewerCountry || 'Unknown Location'}
                            {view.viewerCity && `, ${view.viewerCity}`}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(view.viewedAt).toLocaleDateString()} at{' '}
                            {new Date(view.viewedAt).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                      {view.referrer && (
                        <Badge variant="outline" className="text-xs">
                          {new URL(view.referrer).hostname}
                        </Badge>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Eye className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p className="text-sm">No profile views yet</p>
                    <p className="text-xs">Share your profile to start tracking views</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Downloads */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Download className="w-5 h-5 text-pink-500" />
                  Recent Downloads
                </div>
                <Button variant="outline" size="sm" onClick={() => window.location.href = '/analytics'}>
                  View All
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {safeAnalytics.recentDownloads.length ? (
                  safeAnalytics.recentDownloads.slice(0, 5).map((download) => (
                    <div key={download.id} className="flex items-center justify-between p-3 bg-pink-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                          <Download className="w-4 h-4 text-pink-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {download.downloadType === 'resume_pdf' ? 'Resume PDF' : 'Credential'}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(download.downloadedAt).toLocaleDateString()} at{' '}
                            {new Date(download.downloadedAt).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {download.downloaderCountry || 'Unknown'}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Download className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p className="text-sm">No downloads yet</p>
                    <p className="text-xs">People will see downloads when they get your resume</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Share Profile Modal */}
      {showShareModal && user?.username && (
        <ShareProfile 
          username={user.username} 
          onClose={() => setShowShareModal(false)} 
        />
      )}
    </div>
  );
}
