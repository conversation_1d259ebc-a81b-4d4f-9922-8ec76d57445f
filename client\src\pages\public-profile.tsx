import { useParams } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { generatePDF } from "@/lib/pdfGenerator";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import ShareProfile from "@/components/share-profile";
import { useState, useEffect } from "react";
import { TEMPLATE_STORAGE_KEY, DEFAULT_TEMPLATE, getTemplateById } from "@/lib/cvTemplates";
import { 
  Download, 
  Share, 
  Mail, 
  Phone, 
  MapPin,
  ExternalLink,
  Tag,
  Globe
} from "lucide-react";

export default function PublicProfile() {
  const { username } = useParams();
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>(DEFAULT_TEMPLATE.id);

  const { data: profile, isLoading, error } = useQuery({
    queryKey: ["/api/profile", username],
    enabled: !!username,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Load saved template from localStorage
  useEffect(() => {
    const savedTemplateId = localStorage.getItem(TEMPLATE_STORAGE_KEY);
    if (savedTemplateId && getTemplateById(savedTemplateId)) {
      setSelectedTemplate(savedTemplateId);
    }
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin w-8 h-8 border-4 border-yellow-500 border-t-transparent rounded-full" />
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="pt-6 text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Profile Not Found</h1>
            <p className="text-gray-600">The profile you're looking for doesn't exist.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { user, resume, credentials } = profile;
  const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Professional';

  const handleDownloadPDF = async () => {
    if (!username) return;
    
    try {
      const response = await fetch(`/api/pdf-data/public/${username}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch PDF data');
      }

      const pdfData = await response.json();
      
      // Use the selected template
      generatePDF(pdfData, selectedTemplate);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      // Could add toast notification here if needed
    }
  };

  const handleShareProfile = () => {
    setIsShareDialogOpen(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Profile Header */}
      <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 px-8 py-12 text-white">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
            <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg">
              {resume?.profilePhotoUrl || user.profileImageUrl ? (
                <img 
                  src={resume?.profilePhotoUrl || user.profileImageUrl} 
                  alt={`${fullName} profile picture`} 
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-white bg-opacity-20 flex items-center justify-center text-white text-3xl font-bold">
                  {fullName.charAt(0)}
                </div>
              )}
            </div>
            <div className="text-center md:text-left">
              <h1 className="text-4xl font-bold mb-2">{fullName}</h1>
              <p className="text-xl text-yellow-100 mb-4">{resume?.title || 'Professional'}</p>
              <div className="flex flex-wrap justify-center md:justify-start gap-4 text-yellow-100">
                {user.email && (
                  <div className="flex items-center">
                    <Mail className="w-4 h-4 mr-2" />
                    <span>{user.email}</span>
                  </div>
                )}
                {resume?.phone && (
                  <div className="flex items-center">
                    <Phone className="w-4 h-4 mr-2" />
                    <span>{resume.phone}</span>
                  </div>
                )}
                {resume?.location && (
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-2" />
                    <span>{resume.location}</span>
                  </div>
                )}
                {resume?.website && (
                  <div className="flex items-center">
                    <Globe className="w-4 h-4 mr-2" />
                    <a 
                      href={resume.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="hover:text-white transition-colors underline"
                    >
                      {resume.website.replace(/^https?:\/\//, '')}
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Profile Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Professional Summary */}
        {resume?.summary && (
          <Card className="mb-8">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Professional Summary</h2>
              <p className="text-gray-700 leading-relaxed">{resume.summary}</p>
            </CardContent>
          </Card>
        )}

        {/* Experience */}
        {resume?.experience && resume.experience.length > 0 && (
          <Card className="mb-8">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Work Experience</h2>
              <div className="space-y-6">
                {resume.experience.map((exp, index) => (
                  <div key={index} className="border-l-4 border-yellow-500 pl-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                      <h3 className="text-xl font-semibold text-gray-900">{exp.title}</h3>
                      <span className="text-yellow-600 font-medium">{exp.duration}</span>
                    </div>
                    <p className="text-lg text-gray-700 mb-2">{exp.company}</p>
                    <p className="text-gray-600">{exp.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Skills */}
        {resume?.skills && resume.skills.length > 0 && (
          <Card className="mb-8">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Skills</h2>
              <div className="flex flex-wrap gap-3">
                {resume.skills.map((skill, index) => (
                  <Badge key={index} className="bg-yellow-100 text-yellow-700 px-4 py-2 text-sm font-medium">
                    {skill}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Education */}
        {resume?.education && resume.education.length > 0 && (
          <Card className="mb-8">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Education</h2>
              <div className="space-y-4">
                {resume.education.map((edu, index) => (
                  <div key={index} className="border-l-4 border-yellow-500 pl-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                      <h3 className="text-xl font-semibold text-gray-900">{edu.degree}</h3>
                      <span className="text-yellow-600 font-medium">{edu.year}</span>
                    </div>
                    <p className="text-lg text-gray-700 mb-2">{edu.school}</p>
                    {edu.description && <p className="text-gray-600">{edu.description}</p>}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Credentials */}
        {credentials && credentials.length > 0 && (
          <Card className="mb-8">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Credentials & Certifications</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {credentials.map((cred) => (
                  <div key={cred.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-yellow-300 transition-colors cursor-pointer"
                       onClick={() => {
                         // For data URLs, create a blob and open it
                         if (cred.fileUrl.startsWith('data:')) {
                           const base64Data = cred.fileUrl.split(',')[1];
                           const mimeType = cred.fileUrl.split(',')[0].split(':')[1].split(';')[0];
                           const byteCharacters = atob(base64Data);
                           const byteNumbers = new Array(byteCharacters.length);
                           for (let i = 0; i < byteCharacters.length; i++) {
                             byteNumbers[i] = byteCharacters.charCodeAt(i);
                           }
                           const byteArray = new Uint8Array(byteNumbers);
                           const blob = new Blob([byteArray], { type: mimeType });
                           const url = URL.createObjectURL(blob);
                           window.open(url, '_blank');
                           // Clean up the URL after opening
                           setTimeout(() => URL.revokeObjectURL(url), 1000);
                         } else {
                           window.open(cred.fileUrl, '_blank');
                         }
                       }}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                          <Tag className="text-red-500 w-6 h-6" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{cred.title}</h3>
                          {cred.issuer && <p className="text-sm text-gray-600">{cred.issuer}</p>}
                          {cred.issuedDate && <p className="text-xs text-gray-500">Issued: {cred.issuedDate}</p>}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ExternalLink className="w-4 h-4 text-gray-400" />
                        <span className="text-xs text-gray-500">Click to view</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Download & Share Actions */}
        <Card>
          <CardContent className="p-8">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={handleDownloadPDF}
                className="bg-yellow-500 hover:bg-yellow-600 text-white px-8 py-3 font-semibold"
              >
                <Download className="w-4 h-4 mr-2" />
                Download Resume PDF
              </Button>
              <Button 
                variant="outline"
                onClick={handleShareProfile}
                className="border-2 border-yellow-500 text-yellow-500 hover:bg-yellow-50 px-8 py-3 font-semibold"
              >
                <Share className="w-4 h-4 mr-2" />
                Share Profile
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Share Profile Dialog */}
      {isShareDialogOpen && username && (
        <ShareProfile 
          username={username} 
          onClose={() => setIsShareDialogOpen(false)}
        />
      )}
    </div>
  );
}
