{"timestamp": "2025-07-30T13:57:12.396Z", "webhook": {"id": "test-checkout-123", "status": "COMPLETED", "isPaid": true, "receiptNumber": "RECEIPT-123", "totalAmount": {"value": 99, "currency": "PHP"}}, "requestInfo": {"headers": {"host": "localhost:5000", "user-agent": "curl/8.14.1", "accept": "*/*", "content-type": "application/json", "content-length": "192"}, "ip": "127.0.0.1", "userAgent": "curl/8.14.1", "method": "POST", "url": "/api/webhook", "timestamp": "2025-07-30T13:57:12.396Z", "contentType": "application/json", "contentLength": "192"}, "success": false}