import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { 
  CloudUpload,
  FileText,
  Eye,
  Trash2,
  Loader2,
  Lock,
  Image,
  Edit,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface Credential {
  id: number;
  title: string;
  description?: string;
  fileUrl: string;
  fileName: string;
  fileSize?: number;
  fileType?: string;
  isPublic: boolean;
  issuer?: string;
  issuedDate?: string;
  createdAt: string;
}

interface CredentialUploaderProps {
  credentials: Credential[];
  currentLimit: number;
  userPlan: string;
}

export default function CredentialUploader({ 
  credentials, 
  currentLimit, 
  userPlan 
}: CredentialUploaderProps) {
  // Ensure credentials is always an array
  const safeCredentials = Array.isArray(credentials) ? credentials : [];
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [uploadData, setUploadData] = useState({
    title: "",
    description: "",
    issuer: "",
    issuedDate: "",
    isPublic: false,
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCredential, setEditingCredential] = useState<Credential | null>(null);
  const [editData, setEditData] = useState({
    title: "",
    description: "",
    issuer: "",
    issuedDate: "",
    isPublic: false,
  });

  const uploadMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      return await fetch('/api/credentials/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });
    },
    onSuccess: () => {
      toast({
        title: "Upload Successful",
        description: "Your credential has been uploaded successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/credentials"] });
      setUploadData({
        title: "",
        description: "",
        issuer: "",
        issuedDate: "",
        isPublic: false,
      });
      setSelectedFile(null);
      setIsDialogOpen(false);
    },
    onError: async (error) => {
      const response = error as Response;
      if (response.status === 401) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      
      const errorData = await response.json().catch(() => ({ message: "Upload failed" }));
      toast({
        title: "Upload Failed",
        description: errorData.message || "Failed to upload credential. Please try again.",
        variant: "destructive",
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async (credentialId: number) => {
      return await apiRequest(`/api/credentials/${credentialId}`, { method: "DELETE" });
    },
    onSuccess: () => {
      toast({
        title: "Credential Deleted",
        description: "The credential has been deleted successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/credentials"] });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Delete Failed",
        description: "Failed to delete credential. Please try again.",
        variant: "destructive",
      });
    },
  });

  const editMutation = useMutation({
    mutationFn: async (data: { id: number; updates: any }) => {
      return await apiRequest(`/api/credentials/${data.id}`, {
        method: 'PUT',
        data: data.updates,
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Credential updated successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/credentials"] });
      setEditingCredential(null);
      setEditData({
        title: "",
        description: "",
        issuer: "",
        issuedDate: "",
        isPublic: false,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "Unauthorized",
          description: "Please log in to continue.",
          variant: "destructive",
        });
        return;
      }
      
      toast({
        title: "Error",
        description: "Failed to update credential. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: "Please select a file smaller than 10MB.",
          variant: "destructive",
        });
        return;
      }
      setSelectedFile(file);
      if (!uploadData.title) {
        setUploadData(prev => ({ ...prev, title: file.name }));
      }
    }
  };

  const handleUpload = () => {
    if (!selectedFile) {
      toast({
        title: "No File Selected",
        description: "Please select a file to upload.",
        variant: "destructive",
      });
      return;
    }

    if (!uploadData.title.trim()) {
      toast({
        title: "Title Required",
        description: "Please provide a title for your credential.",
        variant: "destructive",
      });
      return;
    }

    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('title', uploadData.title);
    formData.append('description', uploadData.description);
    formData.append('issuer', uploadData.issuer);
    formData.append('issuedDate', uploadData.issuedDate);
    formData.append('isPublic', uploadData.isPublic.toString());

    uploadMutation.mutate(formData);
  };

  const handleEdit = (credential: Credential) => {
    setEditingCredential(credential);
    setEditData({
      title: credential.title,
      description: credential.description || "",
      issuer: credential.issuer || "",
      issuedDate: credential.issuedDate || "",
      isPublic: credential.isPublic,
    });
  };

  const handleSaveEdit = () => {
    if (!editingCredential) return;
    
    editMutation.mutate({
      id: editingCredential.id,
      updates: editData,
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const getFileIcon = (fileType?: string) => {
    if (fileType?.startsWith('image/')) {
      return <Image className="text-blue-500 w-6 h-6" />;
    }
    return <FileText className="text-red-500 w-6 h-6" />;
  };

  const isAtLimit = safeCredentials.length >= currentLimit;

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Credentials</CardTitle>
          <span className="text-sm text-gray-500">
            {safeCredentials.length}/{currentLimit} uploads ({userPlan} plan)
          </span>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Upload Area */}
        {!isAtLimit ? (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-yellow-400 transition-colors cursor-pointer">
                <CloudUpload className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                <p className="text-gray-600 mb-2">Drag & drop files here, or click to browse</p>
                <p className="text-sm text-gray-500">Supports PDF, JPG, PNG (Max 10MB)</p>
              </div>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Upload Credential</DialogTitle>
                <DialogDescription>
                  Add a new credential to your profile. Supported formats: PDF, JPG, PNG (Max 10MB).
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="file">Select File</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={handleFileSelect}
                    className="mt-1"
                  />
                  {selectedFile && (
                    <p className="text-sm text-gray-600 mt-1">
                      Selected: {selectedFile.name} ({formatFileSize(selectedFile.size)})
                    </p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={uploadData.title}
                    onChange={(e) => setUploadData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="AWS Solutions Architect Certificate"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={uploadData.description}
                    onChange={(e) => setUploadData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Certificate obtained after completing..."
                    rows={3}
                    className="mt-1 resize-none"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="issuer">Issuer</Label>
                    <Input
                      id="issuer"
                      value={uploadData.issuer}
                      onChange={(e) => setUploadData(prev => ({ ...prev, issuer: e.target.value }))}
                      placeholder="Amazon Web Services"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="issuedDate">Issue Date</Label>
                    <Input
                      id="issuedDate"
                      type="date"
                      value={uploadData.issuedDate}
                      onChange={(e) => setUploadData(prev => ({ ...prev, issuedDate: e.target.value }))}
                      className="mt-1"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isPublic"
                    checked={uploadData.isPublic}
                    onCheckedChange={(checked) => setUploadData(prev => ({ ...prev, isPublic: checked }))}
                  />
                  <Label htmlFor="isPublic">Show on public profile</Label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleUpload}
                  disabled={uploadMutation.isPending || !selectedFile}
                  className="bg-yellow-500 hover:bg-yellow-600"
                >
                  {uploadMutation.isPending && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                  Upload
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        ) : (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-50">
            <Lock className="mx-auto mb-2 h-8 w-8 text-gray-400" />
            <p className="text-gray-600 mb-2">Upload limit reached for {userPlan} plan</p>
            <p className="text-sm text-gray-500">
              <a href="/billing" className="text-yellow-500 hover:text-yellow-600 font-medium">
                Upgrade to Pro
              </a> for more uploads
            </p>
          </div>
        )}

        {/* Uploaded Credentials */}
        <div className="space-y-3">
          {safeCredentials.length > 0 ? (
            safeCredentials.map((credential) => (
              <div key={credential.id} className="p-4 bg-gray-50 rounded-lg border">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center border">
                      {getFileIcon(credential.fileType)}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 mb-1">{credential.title}</h4>
                      {credential.description && (
                        <p className="text-sm text-gray-600 mb-2">{credential.description}</p>
                      )}
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span className="flex items-center space-x-1">
                          <FileText className="w-3 h-3" />
                          <span>{formatFileSize(credential.fileSize)}</span>
                        </span>
                        {credential.issuer && (
                          <span>Issued by: {credential.issuer}</span>
                        )}
                        {credential.issuedDate && (
                          <span>Date: {credential.issuedDate}</span>
                        )}
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          credential.isPublic 
                            ? 'bg-green-100 text-green-700' 
                            : 'bg-gray-100 text-gray-700'
                        }`}>
                          {credential.isPublic ? 'Public' : 'Private'}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        // For data URLs, create a blob and open it
                        if (credential.fileUrl.startsWith('data:')) {
                          const base64Data = credential.fileUrl.split(',')[1];
                          const mimeType = credential.fileUrl.split(',')[0].split(':')[1].split(';')[0];
                          const byteCharacters = atob(base64Data);
                          const byteNumbers = new Array(byteCharacters.length);
                          for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                          }
                          const byteArray = new Uint8Array(byteNumbers);
                          const blob = new Blob([byteArray], { type: mimeType });
                          const url = URL.createObjectURL(blob);
                          window.open(url, '_blank');
                          // Clean up the URL after opening
                          setTimeout(() => URL.revokeObjectURL(url), 1000);
                        } else {
                          window.open(credential.fileUrl, '_blank');
                        }
                      }}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(credential)}
                      className="text-gray-600 hover:text-gray-800"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteMutation.mutate(credential.id)}
                      disabled={deleteMutation.isPending}
                      className="text-red-500 hover:text-red-700"
                    >
                      {deleteMutation.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p className="text-sm">No credentials uploaded yet</p>
              <p className="text-xs">Upload your first credential to get started</p>
            </div>
          )}
        </div>

        {isAtLimit && (
          <p className="text-center text-sm text-gray-500 mt-4">
            <Lock className="inline w-4 h-4 mr-1" />
            Upload limit reached. <a href="/billing" className="text-yellow-500 hover:text-yellow-600 font-medium">Upgrade to Pro</a> for unlimited uploads.
          </p>
        )}
      </CardContent>
      
      {/* Edit Credential Dialog */}
      <Dialog open={!!editingCredential} onOpenChange={(open) => !open && setEditingCredential(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Credential</DialogTitle>
            <DialogDescription>
              Update the information for your credential.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-title">Title *</Label>
              <Input
                id="edit-title"
                value={editData.title}
                onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="AWS Solutions Architect Certificate"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={editData.description}
                onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Certificate obtained after completing..."
                rows={3}
                className="mt-1 resize-none"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-issuer">Issuer</Label>
                <Input
                  id="edit-issuer"
                  value={editData.issuer}
                  onChange={(e) => setEditData(prev => ({ ...prev, issuer: e.target.value }))}
                  placeholder="Amazon Web Services"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="edit-issued-date">Issued Date</Label>
                <Input
                  id="edit-issued-date"
                  type="date"
                  value={editData.issuedDate}
                  onChange={(e) => setEditData(prev => ({ ...prev, issuedDate: e.target.value }))}
                  className="mt-1"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="edit-public"
                checked={editData.isPublic}
                onCheckedChange={(checked) => setEditData(prev => ({ ...prev, isPublic: checked }))}
              />
              <Label htmlFor="edit-public" className="text-sm font-medium">
                Show on public profile
              </Label>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setEditingCredential(null)}
              disabled={editMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveEdit}
              disabled={editMutation.isPending}
              className="bg-yellow-500 hover:bg-yellow-600"
            >
              {editMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Credential'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
