import {
  users,
  resumes,
  credentials,
  profileViews,
  downloadEvents,
  credentialUploadEvents,
  payments,
  manualBillingLogs,
  transactionCleanupLogs,
  type User,
  type UpsertUser,
  type Resume,
  type InsertResume,
  type Credential,
  type InsertCredential,
  type InsertProfileView,
  type ProfileView,
  type InsertDownloadEvent,
  type DownloadEvent,
  type InsertCredentialUploadEvent,
  type CredentialUploadEvent,
  type InsertPayment,
  type Payment,
  type ManualBillingLog,
  type InsertManualBillingLog,
  type TransactionCleanupLog,
  type InsertTransactionCleanupLog,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, sql, and, inArray } from "drizzle-orm";

export interface IStorage {
  // User operations (required for Replit Auth)
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  updateUser(userId: string, updates: Partial<UpsertUser>): Promise<User>;
  updateUserMayaInfo(userId: string, mayaCustomerId: string, mayaCheckoutId: string): Promise<User>;
  updateUserType(userId: string, userType: "user" | "admin"): Promise<User>;
  
  // Resume operations
  getResumeByUserId(userId: string): Promise<Resume | undefined>;
  createOrUpdateResume(resume: InsertResume): Promise<Resume>;
  
  // Credential operations
  getCredentialsByUserId(userId: string): Promise<Credential[]>;
  getPublicCredentialsByUserId(userId: string): Promise<Credential[]>;
  getCredentialById(id: number): Promise<Credential | undefined>;
  createCredential(credential: InsertCredential): Promise<Credential>;
  updateCredential(id: number, userId: string, updates: Partial<InsertCredential>): Promise<Credential | null>;
  deleteCredential(id: number, userId: string): Promise<boolean>;
  
  // Plan management
  updateUserPlan(userId: string, plan: "free" | "pro" | "agency"): Promise<User>;
  updateUserSubscription(userId: string, plan: "free" | "pro" | "agency", expiresAt?: Date): Promise<User>;
  
  // Subscription expiry management
  getExpiredSubscriptions(): Promise<User[]>;
  downgradeExpiredUsers(): Promise<{ downgraded: number; users: string[] }>;
  checkUserSubscriptionStatus(userId: string): Promise<{ isExpired: boolean; daysUntilExpiry: number | null }>;
  getSubscriptionsExpiringWithin(days: number): Promise<User[]>;
  
  // Analytics operations
  trackProfileView(profileView: InsertProfileView): Promise<ProfileView>;
  trackDownload(downloadEvent: InsertDownloadEvent): Promise<DownloadEvent>;
  trackCredentialUpload(uploadEvent: InsertCredentialUploadEvent): Promise<CredentialUploadEvent>;
  getProfileViewsCount(userId: string): Promise<number>;
  getDownloadsCount(userId: string): Promise<number>;
  getCredentialUploadsCount(userId: string): Promise<number>;
  getAnalyticsSummary(userId: string): Promise<{
    profileViews: number;
    downloads: number;
    credentialUploads: number;
    recentViews: ProfileView[];
    recentDownloads: DownloadEvent[];
  }>;
  
  // Payment operations
  createPayment(payment: InsertPayment): Promise<Payment>;
  getPaymentByCheckoutId(mayaCheckoutId: string): Promise<Payment | undefined>;
  updatePaymentStatus(mayaCheckoutId: string, status: "pending" | "paid" | "failed" | "cancelled", receiptNumber?: string): Promise<Payment>;
  getUserPayments(userId: string): Promise<Payment[]>;
  getLatestPayment(userId: string): Promise<Payment | undefined>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  async updateUser(userId: string, updates: Partial<UpsertUser>): Promise<User> {
    const [user] = await db
      .update(users)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning();
    return user;
  }

  async updateUserMayaInfo(userId: string, mayaCustomerId: string, mayaCheckoutId: string): Promise<User> {
    const [user] = await db
      .update(users)
      .set({
        mayaCustomerId,
        mayaCheckoutId,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning();
    return user;
  }

  async updateUserType(userId: string, userType: "user" | "admin"): Promise<User> {
    const [user] = await db
      .update(users)
      .set({
        userType,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning();
    return user;
  }

  async getResumeByUserId(userId: string): Promise<Resume | undefined> {
    const [resume] = await db.select().from(resumes).where(eq(resumes.userId, userId));
    return resume;
  }

  async createOrUpdateResume(resumeData: InsertResume): Promise<Resume> {
    const existing = await this.getResumeByUserId(resumeData.userId);
    
    if (existing) {
      const [resume] = await db
        .update(resumes)
        .set({
          userId: resumeData.userId,
          title: resumeData.title,
          summary: resumeData.summary,
          phone: resumeData.phone,
          location: resumeData.location,
          website: resumeData.website,
          profilePhotoUrl: resumeData.profilePhotoUrl,
          skills: resumeData.skills,
          experience: resumeData.experience as any,
          education: resumeData.education as any,
          updatedAt: new Date(),
        })
        .where(eq(resumes.userId, resumeData.userId))
        .returning();
      return resume;
    } else {
      const [resume] = await db
        .insert(resumes)
        .values({
          userId: resumeData.userId,
          title: resumeData.title,
          summary: resumeData.summary,
          phone: resumeData.phone,
          location: resumeData.location,
          website: resumeData.website,
          profilePhotoUrl: resumeData.profilePhotoUrl,
          skills: resumeData.skills,
          experience: resumeData.experience as any,
          education: resumeData.education as any,
        })
        .returning();
      return resume;
    }
  }

  async getCredentialsByUserId(userId: string): Promise<Credential[]> {
    return await db
      .select()
      .from(credentials)
      .where(eq(credentials.userId, userId))
      .orderBy(desc(credentials.createdAt));
  }

  async getPublicCredentialsByUserId(userId: string): Promise<Credential[]> {
    return await db
      .select()
      .from(credentials)
      .where(and(eq(credentials.userId, userId), eq(credentials.isPublic, true)))
      .orderBy(desc(credentials.createdAt));
  }

  async getCredentialById(id: number): Promise<Credential | undefined> {
    const [credential] = await db.select().from(credentials).where(eq(credentials.id, id));
    return credential;
  }

  async createCredential(credentialData: InsertCredential): Promise<Credential> {
    const [credential] = await db
      .insert(credentials)
      .values(credentialData)
      .returning();
    return credential;
  }

  async updateCredential(id: number, userId: string, updates: Partial<InsertCredential>): Promise<Credential | null> {
    try {
      // First verify the user owns this credential
      const credential = await this.getCredentialById(id);
      if (!credential || credential.userId !== userId) {
        return null;
      }
      
      // Update the credential
      const [updatedCredential] = await db
        .update(credentials)
        .set(updates)
        .where(eq(credentials.id, id))
        .returning();
      
      return updatedCredential;
    } catch (error) {
      console.error('Error updating credential:', error);
      return null;
    }
  }

  async deleteCredential(id: number, userId: string): Promise<boolean> {
    try {
      // First verify the user owns this credential
      const credential = await this.getCredentialById(id);
      if (!credential || credential.userId !== userId) {
        return false;
      }
      
      // Delete related credential upload events
      await db
        .delete(credentialUploadEvents)
        .where(eq(credentialUploadEvents.credentialId, id));
      
      // Then delete the credential itself
      const result = await db
        .delete(credentials)
        .where(eq(credentials.id, id));
      
      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      console.error('Error deleting credential:', error);
      return false;
    }
  }

  async updateUserPlan(userId: string, plan: "free" | "pro" | "agency"): Promise<User> {
    const [user] = await db
      .update(users)
      .set({ plan, updatedAt: new Date() })
      .where(eq(users.id, userId))
      .returning();
    return user;
  }

  async updateUserSubscription(userId: string, plan: "free" | "pro" | "agency", expiresAt?: Date): Promise<User> {
    console.log(`🔄 Updating user subscription:`, {
      userId,
      newPlan: plan,
      expiresAt: expiresAt?.toISOString()
    });
    
    const updateData = { 
      plan, 
      subscriptionExpiresAt: expiresAt,
      updatedAt: new Date() 
    };
    
    console.log(`📝 Database update data:`, updateData);

    const [user] = await db
      .update(users)
      .set(updateData)
      .where(eq(users.id, userId))
      .returning();
    
    console.log(`✅ User subscription updated in database:`, {
      id: user.id,
      plan: user.plan,
      subscriptionExpiresAt: user.subscriptionExpiresAt,
      updatedAt: user.updatedAt
    });
    
    return user;
  }

  // Subscription expiry management methods
  async getExpiredSubscriptions(): Promise<User[]> {
    const now = new Date();
    const expiredUsers = await db
      .select()
      .from(users)
      .where(
        and(
          sql`${users.subscriptionExpiresAt} < ${now}`,
          sql`${users.plan} != 'free'`
        )
      );
    return expiredUsers;
  }

  async downgradeExpiredUsers(): Promise<{ downgraded: number; users: string[] }> {
    const expiredUsers = await this.getExpiredSubscriptions();
    const downgradedUserIds: string[] = [];

    for (const user of expiredUsers) {
      await this.updateUserSubscription(user.id, 'free');
      downgradedUserIds.push(user.id);
      console.log(`⬇️  User ${user.id} (${user.email}) downgraded from ${user.plan} to free - subscription expired on ${user.subscriptionExpiresAt}`);
    }

    return {
      downgraded: downgradedUserIds.length,
      users: downgradedUserIds
    };
  }

  async checkUserSubscriptionStatus(userId: string): Promise<{ isExpired: boolean; daysUntilExpiry: number | null }> {
    const user = await this.getUser(userId);
    
    if (!user || !user.subscriptionExpiresAt || user.plan === 'free') {
      return { isExpired: false, daysUntilExpiry: null };
    }

    const now = new Date();
    const expiryDate = new Date(user.subscriptionExpiresAt);
    const timeDiff = expiryDate.getTime() - now.getTime();
    const daysUntilExpiry = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

    return {
      isExpired: daysUntilExpiry <= 0,
      daysUntilExpiry
    };
  }

  async getSubscriptionsExpiringWithin(days: number): Promise<User[]> {
    const now = new Date();
    const futureDate = new Date(now.getTime() + (days * 24 * 60 * 60 * 1000));
    
    const expiringUsers = await db
      .select()
      .from(users)
      .where(
        and(
          sql`${users.subscriptionExpiresAt} BETWEEN ${now} AND ${futureDate}`,
          sql`${users.plan} != 'free'`
        )
      );
    return expiringUsers;
  }

  // Analytics operations
  async trackProfileView(profileView: InsertProfileView): Promise<ProfileView> {
    const [view] = await db
      .insert(profileViews)
      .values(profileView)
      .returning();
    return view;
  }

  async trackDownload(downloadEvent: InsertDownloadEvent): Promise<DownloadEvent> {
    const [download] = await db
      .insert(downloadEvents)
      .values(downloadEvent)
      .returning();
    return download;
  }

  async trackCredentialUpload(uploadEvent: InsertCredentialUploadEvent): Promise<CredentialUploadEvent> {
    const [upload] = await db
      .insert(credentialUploadEvents)
      .values(uploadEvent)
      .returning();
    return upload;
  }

  async getProfileViewsCount(userId: string): Promise<number> {
    const result = await db
      .select({ count: sql<number>`count(*)` })
      .from(profileViews)
      .where(eq(profileViews.userId, userId));
    return result[0]?.count || 0;
  }

  async getDownloadsCount(userId: string): Promise<number> {
    const result = await db
      .select({ count: sql<number>`count(*)` })
      .from(downloadEvents)
      .where(eq(downloadEvents.userId, userId));
    return result[0]?.count || 0;
  }

  async getCredentialUploadsCount(userId: string): Promise<number> {
    const result = await db
      .select({ count: sql<number>`count(*)` })
      .from(credentialUploadEvents)
      .where(eq(credentialUploadEvents.userId, userId));
    return result[0]?.count || 0;
  }

  async getAnalyticsSummary(userId: string): Promise<{
    profileViews: number;
    downloads: number;
    credentialUploads: number;
    recentViews: ProfileView[];
    recentDownloads: DownloadEvent[];
  }> {
    const [profileViewsCount, downloadsCount, credentialUploadsCount, recentViews, recentDownloads] = await Promise.all([
      this.getProfileViewsCount(userId),
      this.getDownloadsCount(userId),
      this.getCredentialUploadsCount(userId),
      db
        .select()
        .from(profileViews)
        .where(eq(profileViews.userId, userId))
        .orderBy(desc(profileViews.viewedAt))
        .limit(10),
      db
        .select()
        .from(downloadEvents)
        .where(eq(downloadEvents.userId, userId))
        .orderBy(desc(downloadEvents.downloadedAt))
        .limit(10),
    ]);

    return {
      profileViews: profileViewsCount,
      downloads: downloadsCount,
      credentialUploads: credentialUploadsCount,
      recentViews,
      recentDownloads,
    };
  }

  // Payment operations
  async createPayment(paymentData: InsertPayment): Promise<Payment> {
    const [payment] = await db
      .insert(payments)
      .values(paymentData)
      .returning();
    return payment;
  }

  async getUserPendingPayments(userId: string): Promise<Payment[]> {
    const pendingPayments = await db
      .select()
      .from(payments)
      .where(and(
        eq(payments.userId, userId),
        eq(payments.status, 'pending')
      ))
      .orderBy(desc(payments.createdAt));
    
    return pendingPayments;
  }

  async cleanupExpiredPendingPayments(userId: string): Promise<number> {
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    
    // Get expired pending payments
    const expiredPayments = await db
      .select()
      .from(payments)
      .where(and(
        eq(payments.userId, userId),
        eq(payments.status, 'pending'),
        lt(payments.createdAt, thirtyMinutesAgo)
      ));

    if (expiredPayments.length === 0) {
      return 0;
    }

    const expiredPaymentIds = expiredPayments.map(p => p.id);

    // Update status to expired instead of deleting
    const result = await db
      .update(payments)
      .set({ 
        status: 'expired',
        updatedAt: new Date()
      })
      .where(inArray(payments.id, expiredPaymentIds));

    console.log(`🧹 Auto-cleanup: Marked ${expiredPayments.length} expired pending payments for user ${userId}`);

    // Log cleanup activity
    for (const payment of expiredPayments) {
      await db
        .insert(transactionCleanupLogs)
        .values({
          paymentId: payment.id,
          userId: userId,
          cleanupReason: 'auto_expired_30min',
          cleanedUpBy: 'system',
          cleanedUpAt: new Date(),
        });
    }

    return expiredPayments.length;
  }

  async cleanupAllExpiredPendingPayments(): Promise<number> {
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    
    // Get all expired pending payments across all users
    const expiredPayments = await db
      .select()
      .from(payments)
      .where(and(
        eq(payments.status, 'pending'),
        lt(payments.createdAt, thirtyMinutesAgo)
      ));

    if (expiredPayments.length === 0) {
      return 0;
    }

    const expiredPaymentIds = expiredPayments.map(p => p.id);

    // Update status to expired
    await db
      .update(payments)
      .set({ 
        status: 'expired',
        updatedAt: new Date()
      })
      .where(inArray(payments.id, expiredPaymentIds));

    console.log(`🧹 System cleanup: Marked ${expiredPayments.length} expired pending payments as expired`);

    // Log cleanup activity for each payment
    for (const payment of expiredPayments) {
      await db
        .insert(transactionCleanupLogs)
        .values({
          paymentId: payment.id,
          userId: payment.userId,
          cleanupReason: 'auto_expired_30min_system',
          cleanedUpBy: 'system',
          cleanedUpAt: new Date(),
        });
    }

    return expiredPayments.length;
  }

  async getAllPendingPayments(): Promise<Payment[]> {
    const pendingPayments = await db
      .select()
      .from(payments)
      .where(eq(payments.status, 'pending'))
      .orderBy(desc(payments.createdAt));
    
    return pendingPayments;
  }

  async getPaymentByCheckoutId(mayaCheckoutId: string): Promise<Payment | undefined> {
    const [payment] = await db
      .select()
      .from(payments)
      .where(eq(payments.mayaCheckoutId, mayaCheckoutId));
    return payment;
  }

  async updatePaymentStatus(mayaCheckoutId: string, status: "pending" | "paid" | "failed" | "cancelled" | "expired", receiptNumber?: string): Promise<Payment> {
    console.log(`💳 Updating payment status:`, {
      checkoutId: mayaCheckoutId,
      newStatus: status,
      receiptNumber
    });
    
    const updateData: any = {
      status,
      updatedAt: new Date(),
    };
    
    if (status === "paid") {
      updateData.paidAt = new Date();
    }
    
    if (receiptNumber) {
      updateData.receiptNumber = receiptNumber;
    }

    console.log(`📝 Payment update data:`, updateData);

    const [payment] = await db
      .update(payments)
      .set(updateData)
      .where(eq(payments.mayaCheckoutId, mayaCheckoutId))
      .returning();
    
    console.log(`✅ Payment status updated:`, {
      id: payment.id,
      checkoutId: payment.mayaCheckoutId,
      status: payment.status,
      paidAt: payment.paidAt,
      receiptNumber: payment.receiptNumber
    });
    
    return payment;
  }

  async getUserPayments(userId: string): Promise<Payment[]> {
    return db
      .select()
      .from(payments)
      .where(eq(payments.userId, userId))
      .orderBy(desc(payments.createdAt));
  }

  async getLatestPayment(userId: string): Promise<Payment | undefined> {
    const [payment] = await db
      .select()
      .from(payments)
      .where(eq(payments.userId, userId))
      .orderBy(desc(payments.createdAt))
      .limit(1);
    return payment;
  }

  async getAllPayments(): Promise<Payment[]> {
    return db
      .select()
      .from(payments)
      .orderBy(desc(payments.createdAt));
  }

  // Manual billing management methods
  async getAllUsersWithBillingInfo(): Promise<(User & { 
    latestPayment?: Payment, 
    totalPayments: number 
  })[]> {
    const allUsers = await db.select().from(users);
    
    const usersWithBilling = await Promise.all(
      allUsers.map(async (user) => {
        const [latestPayment, totalPayments] = await Promise.all([
          this.getLatestPayment(user.id),
          db.select({ count: sql<number>`count(*)` })
            .from(payments)
            .where(eq(payments.userId, user.id))
            .then(result => result[0]?.count || 0)
        ]);
        
        return {
          ...user,
          latestPayment,
          totalPayments
        };
      })
    );
    
    return usersWithBilling;
  }

  async updateUserBillingManually(
    userId: string,
    newStatus: "free" | "pro" | "agency",
    reason: string,
    newExpiryDate?: Date,
    transactionReference?: string
  ): Promise<{ user: User; log: ManualBillingLog }> {
    // Get current user state for logging
    const currentUser = await this.getUser(userId);
    if (!currentUser) {
      throw new Error(`User not found: ${userId}`);
    }

    // Update user subscription
    const updatedUser = await this.updateUserSubscription(userId, newStatus, newExpiryDate);
    
    // If upgrading to pro/agency, mark the latest pending payment as paid
    if (newStatus !== 'free') {
      const latestPendingPayment = await db
        .select()
        .from(payments)
        .where(and(
          eq(payments.userId, userId),
          eq(payments.status, 'pending')
        ))
        .orderBy(desc(payments.createdAt))
        .limit(1);
      
      if (latestPendingPayment.length > 0) {
        const paymentToUpdate = latestPendingPayment[0];
        await db
          .update(payments)
          .set({
            status: 'paid',
            paidAt: new Date(),
            receiptNumber: transactionReference || `ADMIN-${Date.now()}`,
            updatedAt: new Date(),
          })
          .where(eq(payments.id, paymentToUpdate.id));
        
        console.log(`💳 PAYMENT UPDATE: Marked payment ${paymentToUpdate.mayaCheckoutId} as paid for user ${userId}`);
      }
    }
    
    // Log the manual update
    const [log] = await db
      .insert(manualBillingLogs)
      .values({
        userId,
        oldStatus: currentUser.plan || 'free',
        newStatus: newStatus,
        oldExpiryDate: currentUser.subscriptionExpiresAt,
        newExpiryDate: newExpiryDate || null,
        transactionReference: transactionReference || null,
        reason,
      })
      .returning();

    console.log(`👨‍💼 ADMIN BILLING UPDATE: Updated user ${userId} from ${currentUser.plan} to ${newStatus}. Reason: ${reason}`);
    
    return { user: updatedUser, log };
  }

  async cleanupRedundantTransactions(
    userId: string,
    reason: string
  ): Promise<{ removedCount: number; log: TransactionCleanupLog }> {
    // Get all pending payments for user, sorted by creation date
    const pendingPayments = await db
      .select()
      .from(payments)
      .where(and(
        eq(payments.userId, userId),
        eq(payments.status, 'pending')
      ))
      .orderBy(desc(payments.createdAt));

    if (pendingPayments.length <= 1) {
      // Keep at least 1 pending payment or no cleanup needed
      return {
        removedCount: 0,
        log: await db.insert(transactionCleanupLogs)
          .values({ userId, removedTransactions: 0, cleanupReason: `${reason} (No cleanup needed)` })
          .returning()
          .then(result => result[0])
      };
    }

    // Keep the most recent pending payment, remove the rest
    const paymentsToRemove = pendingPayments.slice(1);
    const paymentIdsToRemove = paymentsToRemove.map(p => p.id);

    // Delete the redundant payments using proper Drizzle syntax
    if (paymentIdsToRemove.length > 0) {
      await db
        .delete(payments)
        .where(inArray(payments.id, paymentIdsToRemove));
    }

    // Log the cleanup
    const [log] = await db
      .insert(transactionCleanupLogs)
      .values({
        userId,
        removedTransactions: paymentIdsToRemove.length,
        cleanupReason: reason,
      })
      .returning();

    console.log(`🧹 ADMIN CLEANUP: Removed ${paymentIdsToRemove.length} redundant pending payments for user ${userId}`);

    return { removedCount: paymentIdsToRemove.length, log };
  }

  async getManualBillingLogs(limit: number = 50): Promise<(ManualBillingLog & { user: Pick<User, 'email' | 'firstName' | 'lastName'> | null })[]> {
    return db
      .select({
        id: manualBillingLogs.id,
        userId: manualBillingLogs.userId,
        oldStatus: manualBillingLogs.oldStatus,
        newStatus: manualBillingLogs.newStatus,
        oldExpiryDate: manualBillingLogs.oldExpiryDate,
        newExpiryDate: manualBillingLogs.newExpiryDate,
        transactionReference: manualBillingLogs.transactionReference,
        reason: manualBillingLogs.reason,
        createdAt: manualBillingLogs.createdAt,
        user: {
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
        }
      })
      .from(manualBillingLogs)
      .leftJoin(users, eq(manualBillingLogs.userId, users.id))
      .orderBy(desc(manualBillingLogs.createdAt))
      .limit(limit);
  }

  async getPendingTransactions(): Promise<(Payment & { user: Pick<User, 'email' | 'firstName' | 'lastName'> | null })[]> {
    return db
      .select({
        id: payments.id,
        userId: payments.userId,
        mayaCheckoutId: payments.mayaCheckoutId,
        referenceNumber: payments.referenceNumber,
        amount: payments.amount,
        currency: payments.currency,
        status: payments.status,
        paymentMethod: payments.paymentMethod,
        receiptNumber: payments.receiptNumber,
        paidAt: payments.paidAt,
        createdAt: payments.createdAt,
        updatedAt: payments.updatedAt,
        user: {
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
        }
      })
      .from(payments)
      .leftJoin(users, eq(payments.userId, users.id))
      .where(eq(payments.status, 'pending'))
      .orderBy(desc(payments.createdAt));
  }
}

export const storage = new DatabaseStorage();
