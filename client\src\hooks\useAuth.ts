import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { queryClient } from "@/lib/queryClient";

export function useAuth() {
  const { data: user, isLoading, error } = useQuery({
    queryKey: ["/api/auth/user"],
    retry: false,
    staleTime: 1000 * 60 * 10, // 10 minutes - user info doesn't change often
    refetchOnMount: false, // Don't refetch if data exists and isn't stale
    refetchOnWindowFocus: false,
    refetchInterval: false,
    retryOnMount: false,
  });

  const isAuthenticated = !!user;

  // Preload resume and credentials data when user is authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading && user?.id) {
      // Prefetch resume data
      queryClient.prefetchQuery({
        queryKey: ["/api/resume"],
        staleTime: 1000 * 60 * 5, // 5 minutes
      });

      // Prefetch credentials data
      queryClient.prefetchQuery({
        queryKey: ["/api/credentials"],
        staleTime: 1000 * 60 * 5, // 5 minutes
      });
    }
  }, [isAuthenticated, isLoading, user?.id]);

  return {
    user,
    isLoading,
    isAuthenticated,
    error,
  };
}
