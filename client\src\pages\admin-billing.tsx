import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Users, CreditCard, Calendar, Trash2, <PERSON>, AlertTriangle } from "lucide-react";

interface UserBilling {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  plan: string;
  subscriptionExpiresAt?: string;
  latestPayment?: {
    id: number;
    status: string;
    amount: number;
    createdAt: string;
    referenceNumber: string;
  };
  totalPayments: number;
}

interface PendingTransaction {
  id: number;
  userId: string;
  mayaCheckoutId: string;
  referenceNumber: string;
  amount: number;
  createdAt: string;
  user: {
    email: string;
    firstName?: string;
    lastName?: string;
  };
}

export default function AdminBilling() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [selectedUser, setSelectedUser] = useState<UserBilling | null>(null);
  const [updateForm, setUpdateForm] = useState({
    newStatus: "free",
    newExpiryDate: "",
    transactionReference: "",
    reason: ""
  });

  // Fetch all users with billing info
  const { data: users = [], isLoading: usersLoading } = useQuery({
    queryKey: ['/api/admin/users-billing'],
    staleTime: 30000
  });

  // Fetch pending transactions
  const { data: pendingTransactions = [], isLoading: pendingLoading } = useQuery({
    queryKey: ['/api/admin/pending-transactions'],
    staleTime: 30000
  });

  // Fetch billing logs
  const { data: billingLogs = [], isLoading: logsLoading } = useQuery({
    queryKey: ['/api/admin/billing-logs'],
    staleTime: 30000
  });

  // Update billing mutation
  const updateBillingMutation = useMutation({
    mutationFn: async (data: any) => {
      return await apiRequest('/api/admin/update-billing', {
        method: 'POST',
        data: data
      });
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: data.message,
        variant: "default"
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users-billing'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/billing-logs'] });
      setSelectedUser(null);
      setUpdateForm({ newStatus: "free", newExpiryDate: "", transactionReference: "", reason: "" });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update billing",
        variant: "destructive"
      });
    }
  });

  // Cleanup transactions mutation
  const cleanupMutation = useMutation({
    mutationFn: async (data: { userId: string; reason: string }) => {
      return await apiRequest('/api/admin/cleanup-transactions', {
        method: 'POST',
        data: data
      });
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: data.message,
        variant: "default"
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-transactions'] });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to cleanup transactions",
        variant: "destructive"
      });
    }
  });

  const handleUpdateBilling = () => {
    if (!selectedUser || !updateForm.reason) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    updateBillingMutation.mutate({
      userId: selectedUser.id,
      newStatus: updateForm.newStatus,
      newExpiryDate: updateForm.newExpiryDate || null,
      transactionReference: updateForm.transactionReference || null,
      reason: updateForm.reason
    });
  };

  const handleCleanupTransactions = (userId: string, userEmail: string) => {
    const reason = `Manual cleanup for user ${userEmail} - removing redundant pending transactions`;
    cleanupMutation.mutate({ userId, reason });
  };

  // Filter users based on search and status
  const filteredUsers = (users as UserBilling[]).filter((user: UserBilling) => {
    const matchesSearch = !searchTerm || 
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.id.includes(searchTerm) ||
      `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === "all" || 
      (filterStatus === "free" && user.plan === "free") ||
      (filterStatus === "pro" && user.plan === "pro") ||
      (filterStatus === "pending" && user.latestPayment?.status === "pending");

    return matchesSearch && matchesStatus;
  });

  const getPlanBadgeColor = (plan: string) => {
    switch (plan) {
      case 'pro': return 'bg-green-100 text-green-800';
      case 'agency': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="mb-8 text-center">
        <div className="flex items-center justify-center gap-3 mb-2">
          <Users className="w-8 h-8 text-yellow-600" />
          <h1 className="text-4xl font-bold text-gray-900">Admin Billing Management</h1>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Manually manage user subscriptions, clean up payment records, and maintain billing integrity across the platform.
        </p>
      </div>

      <div className="grid gap-6">
        {/* Users Management Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Users & Billing Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Search and Filter */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <Label htmlFor="search">Search Users</Label>
                <Input
                  id="search"
                  placeholder="Search by email, name, or user ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="w-48">
                <Label htmlFor="filter">Filter by Status</Label>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Users</SelectItem>
                    <SelectItem value="free">Free</SelectItem>
                    <SelectItem value="pro">Pro</SelectItem>
                    <SelectItem value="pending">Pending Payment</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Users Table */}
            {usersLoading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Plan</TableHead>
                    <TableHead>Expires</TableHead>
                    <TableHead>Latest Payment</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user: UserBilling) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{user.firstName} {user.lastName}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                          <div className="text-xs text-gray-400">ID: {user.id}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getPlanBadgeColor(user.plan)}>
                          {user.plan.toUpperCase()}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {user.subscriptionExpiresAt ? (
                          <div className="text-sm">
                            {new Date(user.subscriptionExpiresAt).toLocaleDateString()}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {user.latestPayment ? (
                          <div>
                            <Badge className={getStatusBadgeColor(user.latestPayment.status)}>
                              {user.latestPayment.status}
                            </Badge>
                            <div className="text-xs text-gray-500 mt-1">
                              ₱{(user.latestPayment.amount / 100).toFixed(2)}
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400">No payments</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => {
                                setSelectedUser(user);
                                setUpdateForm({
                                  newStatus: user.plan as any,
                                  newExpiryDate: user.subscriptionExpiresAt ? 
                                    new Date(user.subscriptionExpiresAt).toISOString().split('T')[0] : "",
                                  transactionReference: user.latestPayment?.referenceNumber || "",
                                  reason: ""
                                });
                              }}
                            >
                              Update
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Update Billing for {user.email}</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <Label htmlFor="newStatus">New Status</Label>
                                <Select 
                                  value={updateForm.newStatus} 
                                  onValueChange={(value) => 
                                    setUpdateForm(prev => ({ ...prev, newStatus: value }))
                                  }
                                >
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="free">Free</SelectItem>
                                    <SelectItem value="pro">Pro</SelectItem>
                                    <SelectItem value="agency">Agency</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <Label htmlFor="newExpiryDate">Expiry Date</Label>
                                <Input
                                  id="newExpiryDate"
                                  type="date"
                                  value={updateForm.newExpiryDate}
                                  onChange={(e) => 
                                    setUpdateForm(prev => ({ ...prev, newExpiryDate: e.target.value }))
                                  }
                                />
                              </div>
                              <div>
                                <Label htmlFor="transactionReference">Transaction Reference</Label>
                                <Input
                                  id="transactionReference"
                                  placeholder="Maya checkout ID or receipt number"
                                  value={updateForm.transactionReference}
                                  onChange={(e) => 
                                    setUpdateForm(prev => ({ ...prev, transactionReference: e.target.value }))
                                  }
                                />
                              </div>
                              <div>
                                <Label htmlFor="reason">Reason for Update*</Label>
                                <Textarea
                                  id="reason"
                                  placeholder="e.g., Manually verified Maya payment, User upgrade request..."
                                  value={updateForm.reason}
                                  onChange={(e) => 
                                    setUpdateForm(prev => ({ ...prev, reason: e.target.value }))
                                  }
                                  rows={3}
                                />
                              </div>
                              <Button 
                                onClick={handleUpdateBilling}
                                disabled={updateBillingMutation.isPending || !updateForm.reason}
                                className="w-full"
                              >
                                {updateBillingMutation.isPending ? "Updating..." : "Update Billing"}
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Pending Transactions Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5" />
              Pending Transactions Cleanup
            </CardTitle>
          </CardHeader>
          <CardContent>
            {pendingLoading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ) : (pendingTransactions as any[]).length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                No pending transactions found
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Checkout ID</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {(pendingTransactions as any[]).map((transaction: any) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {transaction.user.firstName} {transaction.user.lastName}
                          </div>
                          <div className="text-sm text-gray-500">{transaction.user.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-mono">{transaction.mayaCheckoutId}</div>
                        <div className="text-xs text-gray-500">{transaction.referenceNumber}</div>
                      </TableCell>
                      <TableCell>₱{(transaction.amount / 100).toFixed(2)}</TableCell>
                      <TableCell>{new Date(transaction.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCleanupTransactions(transaction.userId, transaction.user.email)}
                          disabled={cleanupMutation.isPending}
                        >
                          <Trash2 className="w-4 h-4 mr-1" />
                          Cleanup
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Billing Logs Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="w-5 h-5" />
              Recent Billing Updates
            </CardTitle>
          </CardHeader>
          <CardContent>
            {logsLoading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Admin</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Change</TableHead>
                    <TableHead>Reason</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {(billingLogs as any[]).map((log: any) => (
                    <TableRow key={log.id}>
                      <TableCell>Admin</TableCell>
                      <TableCell>{log.user?.email || 'Unknown'}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Badge className={getPlanBadgeColor(log.oldStatus)}>
                            {log.oldStatus}
                          </Badge>
                          <span>→</span>
                          <Badge className={getPlanBadgeColor(log.newStatus)}>
                            {log.newStatus}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell className="max-w-xs truncate">{log.reason}</TableCell>
                      <TableCell>{new Date(log.createdAt).toLocaleDateString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}