We are using Maya for payment processing in a full-stack app on Replit. The Maya dashboard correctly marks the transaction as SUCCESS, but in our system:

The user is still tagged as pending in the payments table.

The users table is not updated (no plan upgrade or expiration date).

The React frontend still reflects a "Free" status even after a successful payment.

💻 Our Stack:
Frontend:

React 18 + TypeScript

Tailwind CSS + shadcn/ui

Wouter (routing)

React Query

React Hook Form + Zod

Vite

Backend:

Node.js with Express (ESM)

Maya Integration (via webhooks)

Replit Auth (OIDC)

Express sessions using PostgreSQL

Database:

Neon PostgreSQL

Drizzle ORM with WebSocket

Drizzle Kit for migrations

🗃️ Relevant Database Fields:
payments table:
status — should be updated to 'paid'

receipt_number — from Maya API/webhook

paid_at — timestamp of successful payment

users table:
plan — should be 'pro'

subscription_expires_at — should be 30 days from paid_at

🔍 Issues to Investigate:
✅ Webhook Debug

Are you properly logging the Maya webhook payload?

Is the webhook status being correctly checked? (<PERSON> returns 'SUCCESS')

Are you using the correct identifier (referenceNumber, paymentId, or receiptNumber) to match with your database?

🧾 Drizzle ORM Update Logic

The webhook should:

Locate the payment by reference_number

Update the fields in payments

Find the related user

Set:

ts
Copy
Edit
user.plan = 'pro';
user.subscription_expires_at = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
🔄 Frontend Status Sync

Does the React app refetch the user's plan via React Query after redirection?

Is there any caching or stale session data masking the real state?

🚦 Webhook Firing Confirmation

Is the webhook URL publicly accessible?

Can you confirm Maya is calling your /api/webhooks/maya endpoint?

Is there any 500 error or crash in your Express route?

🎯 Goal:
Maya sends a SUCCESS webhook.

payments.status, receipt_number, paid_at are correctly updated.

User gets plan = 'pro', and subscription_expires_at set to +30 days.

React frontend reflects this in near real-time after query refetch.