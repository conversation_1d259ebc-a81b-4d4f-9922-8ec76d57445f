export interface CVTemplate {
  id: string;
  name: string;
  description: string;
  category: 'modern' | 'minimalist' | 'creative' | 'classic';
  thumbnail: string;
  primaryColor: string;
  secondaryColor: string;
  layout: 'single-column' | 'two-column' | 'sidebar-left' | 'sidebar-right';
}

export const CV_TEMPLATES: CVTemplate[] = [
  {
    id: 'modern-blue',
    name: 'Modern Professional',
    description: 'Clean and contemporary design with blue accents',
    category: 'modern',
    thumbnail: '/templates/modern-blue.svg',
    primaryColor: '#3B82F6',
    secondaryColor: '#EFF6FF',
    layout: 'sidebar-left'
  },
  {
    id: 'minimalist-gray',
    name: 'Minimalist Clean',
    description: 'Simple and elegant with minimal design elements',
    category: 'minimalist',
    thumbnail: '/templates/minimalist-gray.svg',
    primaryColor: '#6B7280',
    secondaryColor: '#F9FAFB',
    layout: 'single-column'
  },
  {
    id: 'creative-orange',
    name: 'Creative Bold',
    description: 'Vibrant and creative design for artistic professionals',
    category: 'creative',
    thumbnail: '/templates/creative-orange.svg',
    primaryColor: '#F97316',
    secondaryColor: '#FFF7ED',
    layout: 'two-column'
  },
  {
    id: 'classic-navy',
    name: 'Classic Professional',
    description: 'Traditional and formal design for corporate roles',
    category: 'classic',
    thumbnail: '/templates/classic-navy.svg',
    primaryColor: '#1E40AF',
    secondaryColor: '#F0F9FF',
    layout: 'sidebar-right'
  }
];

export const getTemplateById = (templateId: string): CVTemplate | undefined => {
  return CV_TEMPLATES.find(template => template.id === templateId);
};

export const getTemplatesByCategory = (category: CVTemplate['category']): CVTemplate[] => {
  return CV_TEMPLATES.filter(template => template.category === category);
};

// Default template for new users
export const DEFAULT_TEMPLATE = CV_TEMPLATES[0];

// Template storage key for localStorage
export const TEMPLATE_STORAGE_KEY = 'juancv-selected-template';