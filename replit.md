# JuanCV - Modern Resume Builder Platform

## Overview

JuanCV is a full-stack web application that serves as a modern resume builder and credential uploader platform. Users can create professional resumes, upload credentials (certificates, diplomas, IDs), and share their profiles via personalized links. The platform features a freemium monetization model with Free, Pro, and Agency tiers.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui component library
- **Routing**: Wouter for client-side routing
- **State Management**: TanStack Query (React Query) for server state
- **Forms**: React Hook Form with Zod validation
- **Build Tool**: Vite with hot module replacement

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ESM modules
- **Authentication**: Replit Auth with OpenID Connect
- **Session Management**: Express sessions with PostgreSQL storage
- **File Processing**: Multer for multipart uploads
- **Payment Processing**: PayMongo integration (Philippine-focused)

### Database Architecture
- **Primary Database**: PostgreSQL via Neon serverless
- **ORM**: Drizzle ORM with type-safe schema definitions
- **Connection**: Neon serverless driver with WebSocket support
- **Migration Strategy**: Drizzle Kit for schema migrations

## Key Components

### Data Models
- **Users**: Authentication, profile, and subscription management with expiry tracking
- **Resumes**: Structured resume data with skills, experience, education, and profile photos
- **Credentials**: File uploads with metadata and visibility controls
- **Sessions**: Secure session storage for authentication
- **Payments**: Payment tracking with Maya integration and subscription renewal

### Authentication System
- **Provider**: Replit Auth with OIDC
- **Session Storage**: PostgreSQL-backed sessions
- **Authorization**: JWT-based token validation
- **User Management**: Automatic user creation and profile updates

### File Management
- **Upload Handler**: Multer with memory storage for photos and credentials
- **Photo Storage**: Base64 encoding stored directly in database
- **File Types**: PDF, JPG, PNG with 10MB size limit
- **Access Control**: Public/private visibility settings

### Public Profile & Sharing
- **Public URLs**: Username-based profile sharing (e.g., /profile/username)
- **QR Code Generation**: Dynamic QR codes for profile sharing
- **Social Sharing**: WhatsApp, Email, Messenger integration
- **PDF Generation**: Downloadable resumes with profile photos
- **Analytics Tracking**: Profile views and download events

### Payment Integration
- **Provider**: PayMongo with hosted checkout integration
- **Subscription Model**: Monthly recurring billing with expiry tracking
- **Plan Tiers**: Free (limited), Pro (₱99/mo), Agency (enterprise)
- **Feature Gating**: Plan-based feature restrictions
- **Payment Methods**: Credit/Debit Cards, GCash, GrabPay, PayMaya
- **Subscription Tracking**: 30-day expiry periods, automatic renewal notifications
- **Expiry Management**: Visual warnings for expiring/expired subscriptions

## Data Flow

### User Registration/Login
1. User authenticates via Replit Auth
2. OIDC token validation and user creation/update
3. Session establishment with PostgreSQL storage
4. Redirect to dashboard with authenticated state

### Resume Creation
1. Form submission with structured data validation
2. Zod schema validation on client and server
3. Drizzle ORM upsert operation
4. Real-time UI updates via React Query

### Credential Upload
1. File selection with client-side validation
2. Multer multipart processing
3. Cloudinary upload with transformation
4. Database metadata storage
5. UI refresh with new credential list

### Public Profile Sharing
1. Username-based route resolution
2. Public data aggregation (resume + public credentials)
3. SEO-optimized rendering
4. PDF generation with profile photos
5. QR code generation for easy sharing
6. Social media sharing integration
7. Analytics tracking for views and downloads

## External Dependencies

### Core Infrastructure
- **Neon Database**: Serverless PostgreSQL hosting
- **Cloudinary**: File storage and image processing
- **PayMongo**: Payment processing and subscription management (Philippine-focused)
- **Replit**: Authentication provider and development platform

### Development Tools
- **TypeScript**: Type safety across the stack
- **Vite**: Development server and build optimization
- **Tailwind CSS**: Utility-first styling framework
- **shadcn/ui**: Pre-built component library

### Third-party Libraries
- **Drizzle ORM**: Type-safe database operations
- **React Query**: Server state management
- **React Hook Form**: Form handling and validation
- **Zod**: Runtime type validation
- **Wouter**: Lightweight routing

## Deployment Strategy

### Build Process
- **Frontend**: Vite production build with asset optimization
- **Backend**: esbuild bundling for Node.js deployment
- **Assets**: Optimized static file serving

### Environment Configuration
- **Development**: Local Vite dev server with Express API
- **Production**: Single Node.js server serving static files and API
- **Database**: Managed PostgreSQL via Neon
- **File Storage**: Cloudinary CDN integration

### Security Considerations
- **Authentication**: Secure OIDC implementation
- **Session Management**: HTTP-only cookies with CSRF protection
- **File Uploads**: Type and size validation
- **API Security**: Input validation and rate limiting
- **Environment Variables**: Secure secret management

### Scalability Design
- **Database**: Connection pooling with Neon serverless
- **File Storage**: CDN-delivered assets via Cloudinary
- **Caching**: React Query for client-side caching
- **Monitoring**: Request logging and error tracking

## Recent Updates

### Template Selection System (July 18, 2025)
- **Added Multiple CV Templates**: Implemented 4 distinct template layouts (Modern, Minimalist, Creative, Classic)
- **Template Selector UI**: Created comprehensive template selection interface with:
  - Interactive template grid with hover effects and previews
  - Category-based filtering (Modern, Minimalist, Creative, Classic)
  - Template preview modal with full-size thumbnails
  - Color palette indicators for each template
  - Template persistence via localStorage
- **Enhanced PDF Generation**: Extended PDF generator to support multiple template layouts:
  - Sidebar Left layout with contact info and skills in left sidebar
  - Sidebar Right layout with contact info and skills in right sidebar
  - Two-column layout with header banner and split content
  - Single-column layout (original/default)
  - Template-specific color schemes and styling
- **Dashboard Integration**: Added tabbed interface to dashboard with dedicated Templates tab
- **Template Persistence**: Users' last selected template is saved and restored automatically
- **Professional Template Designs**: Each template features distinct visual styles:
  - Modern Professional: Blue accents with left sidebar
  - Minimalist Clean: Gray tones with single-column layout
  - Creative Bold: Orange accents with two-column design
  - Classic Professional: Navy blue with right sidebar

### Favicon and Branding Implementation (July 21, 2025)
- **Professional Favicon Design**: Created comprehensive favicon set with CV document design in yellow branding
- **Multi-Format Support**: Generated SVG favicons in multiple sizes (16x16, 32x32, 180x180) for optimal browser compatibility
- **Web App Manifest**: Implemented PWA-ready site.webmanifest with proper theme colors and display settings
- **SEO Enhancement**: Added comprehensive meta tags including Open Graph and Twitter Card support
- **Brand Consistency**: Applied #EAB308 yellow theme color across all favicon elements matching JuanCV branding

### Authentication Domain Fix (July 21, 2025)
- **Custom Domain Support**: Fixed authentication error "Unknown authentication strategy" when using custom domain juancv.app
- **Domain Strategy Registration**: Updated authentication system to register strategies for both Replit domains and custom domains
- **Fallback Authentication**: Implemented intelligent fallback mechanism for domain strategy selection
- **Authentication Stability**: Resolved authentication issues across different domain configurations

### Billing Security Enhancement (July 21, 2025)
- **Critical Security Fix**: Fixed billing logic to ensure users are only upgraded to Pro after verified successful payment from Maya
- **Frontend Verification**: Removed premature success assumptions based on URL parameters alone
- **Backend Payment Verification**: Enhanced webhook handler with proper validation, amount verification, and double-processing prevention
- **Manual Payment Security**: Added time-based restrictions and enhanced logging for manual payment completion
- **Payment Flow**: Users now see "Verifying Payment" message instead of immediate success, with proper backend verification before showing actual success

### Critical Security Fix - Maya Payment Auto-Upgrade Bug (July 22, 2025)
- **Security Vulnerability Fixed**: Resolved critical bug where users were auto-upgraded to Pro without completing payment
- **Root Cause**: Auto-completion mechanism in frontend was triggering payment completion for any pending payment when user returned to billing page
- **Frontend Fix**: Removed dangerous auto-completion logic that called `/api/complete-payment` without payment verification
- **Backend Fix**: Disabled manual payment completion endpoint to prevent unauthorized upgrades
- **Security Enhancement**: Payments now only process through verified Maya webhooks with proper amount and status validation
- **User Impact**: Users can no longer exploit the system by clicking "Upgrade to Pro", navigating back without paying, and getting upgraded

### Website/Portfolio Field Integration (July 22, 2025)
- **Public Profile Enhancement**: Added website/portfolio field with Globe icon to public profile header
- **PDF Template Integration**: Added website field to all PDF resume templates (sidebar-left, sidebar-right, two-column, single-column)
- **Template Selection Fix**: Public profile PDF downloads now correctly use selected template from Templates tab
- **Template Persistence**: Template selection properly loads from localStorage on public profile page

### Admin Billing Management System (July 22, 2025)
- **Secure Admin Panel**: Created `/admin/manual-billing` route with role-based access control for specific admin user IDs
- **Manual Subscription Updates**: Comprehensive admin interface to manually update user subscription status (Free/Pro/Agency) with custom expiry dates
- **Transaction Cleanup System**: Remove redundant pending Maya transactions that accumulate after successful webhook processing
- **Comprehensive Audit Trail**: All manual billing changes logged with admin ID, user ID, status changes, reasons, and timestamps in dedicated `manual_billing_logs` table
- **Pending Transaction Management**: View and cleanup orphaned pending payments with transaction cleanup logging in `transaction_cleanup_logs` table
- **Advanced User Search**: Filter users by subscription status, search by email/name/ID, with real-time billing information display
- **Database Schema Enhancements**: Added `manualBillingLogs` and `transactionCleanupLogs` tables with proper relations and type safety
- **Security Features**: Admin middleware validates user permissions, comprehensive error handling, and detailed server-side logging
- **User-Friendly Interface**: Responsive admin dashboard with tables, forms, dialogs, and status badges for efficient billing management

### Admin Billing Management Enhancement (July 22, 2025)
- **Professional Header Design**: Added centered header with icon, title, and descriptive subtitle for the admin billing management page
- **Admin Navigation Integration**: Added "Admin Billing" menu links in desktop, mobile, and dropdown navigation visible only to admin users
- **Automatic Payment Status Updates**: Enhanced manual billing updates to automatically mark pending payments as "paid" when upgrading users
- **Payment History Integration**: Latest pending payment automatically updated with paid status and receipt number during manual upgrades
- **Multi-Platform Menu Access**: Admin billing management accessible from main navigation bar, user dropdown, and mobile menu
- **Visual Admin Distinction**: Admin menu items styled with red color scheme to distinguish from regular user functions
- **Enhanced User Experience**: Clear visual feedback and professional layout for efficient admin workflow management

### Payment Flow Security Enhancement (July 23, 2025)
- **Secure Payment Creation**: Pending transactions now only created AFTER successful Maya session initialization, not on button click
- **Multiple Payment Prevention**: System prevents users from creating multiple pending payments within 30-minute window
- **Auto-Cleanup System**: Expired pending payments automatically marked as "expired" after 30 minutes with comprehensive logging
- **Enhanced Error Handling**: Clear user feedback for payment conflicts and session creation issues
- **Database Schema Update**: Added "expired" status to payment enum for better transaction lifecycle management
- **Admin Auto-Cleanup**: Admin panel automatically cleans expired payments when viewing pending transactions
- **Improved UX**: Button text shows "Creating secure session..." during Maya integration process

### Simplified Maya Payment System with Window Monitoring (July 25, 2025)
- **Window-Based Payment Flow**: Redesigned payment system to open Maya checkout in popup window instead of full redirect
- **Real-Time Payment Monitoring**: Implemented window messaging system to detect payment completion automatically
- **Smart Fallback System**: Added status polling backup mechanism when window messaging fails
- **Payment Success/Failure Pages**: Created dedicated success and failure pages that communicate with parent window
- **Enhanced User Experience**: Popup window approach keeps main application context while processing payment
- **Automatic Status Updates**: User plan automatically refreshes after successful payment without manual reload
- **Edge Case Handling**: Comprehensive handling of popup blockers, window closing, and payment verification
- **Instant Feedback**: Real-time button states and toast notifications for all payment stages
- **Simplified Architecture**: Streamlined payment flow reduces complexity while maintaining security and reliability

### Maya Payment System Cleanup & Enhancement (July 30, 2025)
- **Codebase Cleanup**: Removed all Maya payment test and debugging endpoints for cleaner production code
- **Removed Components**: Eliminated maya-debug.tsx, payment-test.tsx, and subscription-test.tsx debugging components
- **Cleaned Routes**: Removed test endpoints including /api/test/maya-payment-flow, /api/create-test-payment, /api/test-complete-payment, and subscription test routes
- **File Cleanup**: Removed debug-payments.js and test-real-payment.js testing utilities
- **Production Ready**: Simplified codebase with only essential payment processing functionality remaining
- **Enhanced Webhook Handler**: Added upgrade verification and recovery mechanisms to ensure successful user upgrades
- **Bulletproof User Upgrades**: Implemented double-verification and retry logic for failed user plan upgrades
- **Comprehensive Payment Flow**: Maya webhook processes payment → updates status → upgrades user → verifies upgrade → logs success
- **Verified Functionality**: Confirmed Maya payment system remains fully operational with robust user upgrade handling

### Previous Updates
- **Calendar Date Picker**: Updated credential upload forms to use native date input type
- **Credential Viewing**: Fixed PDF viewing in public profiles with proper blob URL handling
- **Profile Sharing**: Integrated QR code generation and social sharing functionality