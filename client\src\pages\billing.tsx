import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import Navbar from "@/components/navbar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Check,
  Crown,
  Loader2,
  CreditCard,
  Smartphone,
  Wallet,
  History,
  Calendar,
  DollarSign,
  CheckCircle,
} from "lucide-react";

function UpgradeForm() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [paymentWindow, setPaymentWindow] = useState<Window | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'failed'>('idle');

  // Monitor payment window and status
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    let statusPollingId: NodeJS.Timeout;
    let messageListener: (event: MessageEvent) => void;

    if (paymentWindow) {
      // Monitor if window is closed manually
      intervalId = setInterval(() => {
        if (paymentWindow.closed) {
          setPaymentWindow(null);
          setIsLoading(false);
          setPaymentStatus('idle');
          toast({
            title: "Payment Cancelled",
            description: "Payment window was closed. Please try again if you want to upgrade.",
            variant: "destructive",
          });
        }
      }, 1000);

      // Poll payment status every 5 seconds as backup
      statusPollingId = setInterval(async () => {
        try {
          const userResponse = await apiRequest('/api/auth/user');
          console.log('Payment status poll - user plan:', userResponse.plan);
          if (userResponse.plan === 'pro') {
            console.log('✅ Payment successful detected via polling');
            handlePaymentSuccess();
          }
        } catch (error) {
          console.log('Status polling error:', error);
        }
      }, 5000);

      // Listen for messages from payment window
      messageListener = (event: MessageEvent) => {
        console.log('Payment window message received:', event.data, 'from origin:', event.origin);
        // Accept messages from our own domain (payment success/failure pages)
        if (event.origin === window.location.origin) {
          if (event.data.type === 'PAYMENT_SUCCESS') {
            console.log('✅ Payment success message received:', event.data.checkoutId);
            handlePaymentSuccess(event.data.checkoutId);
          } else if (event.data.type === 'PAYMENT_FAILED') {
            console.log('❌ Payment failed message received');
            handlePaymentFailed();
          }
        }
      };

      window.addEventListener('message', messageListener);
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
      if (statusPollingId) clearInterval(statusPollingId);
      if (messageListener) window.removeEventListener('message', messageListener);
    };
  }, [paymentWindow]);

  const handlePaymentSuccess = async (checkoutId?: string) => {
    setPaymentStatus('success');
    setPaymentWindow(null);
    
    toast({
      title: "Payment Successful!",
      description: "Your account has been upgraded to Pro. Refreshing your information...",
    });

    // Refresh user data and redirect to dashboard
    setTimeout(async () => {
      await queryClient.invalidateQueries({ queryKey: ['/api/auth/user'] });
      window.location.reload();
    }, 2000);
  };

  const handlePaymentFailed = () => {
    setPaymentStatus('failed');
    setPaymentWindow(null);
    setIsLoading(false);
    
    toast({
      title: "Payment Failed",
      description: "Your payment could not be processed. Please try again.",
      variant: "destructive",
    });
  };

  const upgradeToProMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("/api/create-checkout-session", {
        method: "POST",
      });
      return response;
    },
    onSuccess: (data) => {
      // Open Maya Checkout in new window instead of redirecting
      const width = 600;
      const height = 700;
      const left = (window.screen.width / 2) - (width / 2);
      const top = (window.screen.height / 2) - (height / 2);
      
      const newWindow = window.open(
        data.checkout_url,
        'maya_payment',
        `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
      );

      if (newWindow) {
        setPaymentWindow(newWindow);
        setPaymentStatus('processing');
        
        // Focus the payment window
        newWindow.focus();
        
        toast({
          title: "Payment Window Opened",
          description: "Complete your payment in the popup window. Don't close this page.",
        });
      } else {
        // Handle popup blocker
        setIsLoading(false);
        toast({
          title: "Popup Blocked",
          description: "Please allow popups for this site and try again, or use the direct payment link.",
          variant: "destructive",
          action: (
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(data.checkout_url, '_blank')}
            >
              Open Payment
            </Button>
          ),
        });
      }
    },
    onError: (error: any) => {
      console.error("Upgrade failed:", error);
      setIsLoading(false);
      
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }

      if (error.message?.includes("pending payment")) {
        toast({
          title: "Payment In Progress",
          description: "You already have a payment being processed. Please wait or try again in 30 minutes.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Payment Error",
          description: error.message || "Failed to initiate payment session",
          variant: "destructive",
        });
      }
    },
  });

  const handleUpgrade = () => {
    if (paymentStatus === 'processing') {
      toast({
        title: "Payment In Progress",
        description: "Please complete the payment in the popup window.",
      });
      return;
    }
    
    setIsLoading(true);
    setPaymentStatus('idle');
    upgradeToProMutation.mutate();
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <p className="text-sm text-gray-600 mb-4">
          A secure payment window will open. Complete your payment there.
        </p>

        <div className="flex justify-center items-center gap-4 mb-6">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Wallet className="w-4 h-4" />
            <span>Maya</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <CreditCard className="w-4 h-4" />
            <span>Cards</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Smartphone className="w-4 h-4" />
            <span>Online Banking</span>
          </div>
        </div>
      </div>

      <Button
        onClick={handleUpgrade}
        disabled={isLoading || paymentStatus === 'processing'}
        className="w-full bg-amber-600 hover:bg-amber-700 disabled:opacity-50"
        size="lg"
      >
        {paymentStatus === 'processing' ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Payment in progress...
          </>
        ) : paymentStatus === 'success' ? (
          <>
            <CheckCircle className="w-4 h-4 mr-2" />
            Payment successful! Refreshing...
          </>
        ) : isLoading ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Opening payment window...
          </>
        ) : (
          <>
            <Crown className="w-4 h-4 mr-2" />
            Upgrade to Pro - ₱99/month
          </>
        )}
      </Button>
      
      {paymentStatus === 'processing' && (
        <div className="text-center text-sm text-muted-foreground">
          <p>Complete your payment in the popup window.</p>
          <p className="text-xs mt-1">Don't close this page until payment is complete.</p>
        </div>
      )}

      <div className="text-xs text-gray-500 text-center">
        <p>Secure payment powered by Maya</p>
        <p>SSL encrypted and PCI DSS compliant</p>
      </div>
    </div>
  );
}



function PaymentHistory({ userId }: { userId: string }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const { data: subscriptionData, isLoading } = useQuery({
    queryKey: [`/api/user/${userId}/subscription`],
    enabled: !!userId,
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="w-5 h-5" />
            Payment History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const payments = subscriptionData?.payments || [];
  const latestPayment = subscriptionData?.latestPayment;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="w-5 h-5" />
          Payment History
        </CardTitle>
      </CardHeader>
      <CardContent>
        {latestPayment && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-800 mb-1">Latest Payment</h4>
            <div className="flex items-center gap-4 text-sm text-green-700">
              <span className="flex items-center gap-1">
                ₱{(latestPayment.amount / 100).toFixed(2)}
              </span>
              <span className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {latestPayment.paidAt
                  ? new Date(latestPayment.paidAt).toLocaleDateString()
                  : "Pending"}
              </span>
              <Badge
                variant={
                  latestPayment.status === "paid" ? "default" : "secondary"
                }
              >
                {latestPayment.status}
              </Badge>
            </div>
          </div>
        )}

        {payments.length === 0 ? (
          <p className="text-gray-500 text-center py-4">
            No payment history yet
          </p>
        ) : (
          <div className="space-y-2">
            {payments.slice(0, 5).map((payment: any) => (
              <div
                key={payment.id}
                className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
              >
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    ₱{(payment.amount / 100).toFixed(2)}
                  </span>
                  <Badge
                    variant={
                      payment.status === "paid" ? "default" : "secondary"
                    }
                    className="text-xs"
                  >
                    {payment.status}
                  </Badge>
                  {payment.status === "pending" && (
                    <span className="text-xs text-orange-600 font-medium">
                      Processing...
                    </span>
                  )}
                </div>
                <span className="text-xs text-gray-500">
                  {payment.paidAt
                    ? new Date(payment.paidAt).toLocaleDateString()
                    : new Date(payment.createdAt).toLocaleDateString()}
                </span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function Billing() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const { toast } = useToast();
  const [paymentStatus, setPaymentStatus] = useState<string | null>(null);
  
  // Fetch subscription data to get expiry date
  const { data: subscriptionData, isLoading: subscriptionLoading, refetch: refetchSubscription } = useQuery({
    queryKey: [`/api/user/${user?.id}/subscription`],
    enabled: !!user?.id,
  });

  // Verify payment status when returning from payment gateway
  useEffect(() => {
    if (paymentStatus === "success" && user?.id && subscriptionData) {
      // Give backend some time to process webhook, then verify
      const verifyPayment = async () => {
        try {
          // Wait a bit for webhook processing
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Refetch user data and subscription data to check if upgrade actually occurred
          await refetchSubscription();
          
          // Check if user is actually upgraded to Pro
          const { data: updatedSubscription } = await refetchSubscription();
          const updatedUser = await apiRequest("/api/auth/user");
          
          if (updatedUser.plan === "pro" || (updatedSubscription?.subscription?.plan === "pro")) {
            toast({
              title: "Payment Successful!",
              description: "Welcome to JuanCV Pro! Your upgrade is now active and all Pro features are unlocked.",
              duration: 5000,
            });
          } else {
            // Payment URL said success but user is not upgraded - likely webhook delay or failure
            toast({
              title: "Payment Processing",
              description: "Your payment is being processed. If your plan doesn't update in a few minutes, please contact support.",
              variant: "default",
              duration: 8000,
            });
            
            // Retry verification after longer delay
            setTimeout(async () => {
              const finalUser = await apiRequest("/api/auth/user");
              if (finalUser.plan !== "pro") {
                toast({
                  title: "Payment Verification Needed",
                  description: "We're still processing your payment. Please check your email or contact support if you continue to see this message.",
                  variant: "destructive",
                  duration: 10000,
                });
              }
            }, 30000);
          }
        } catch (error) {
          console.error("Payment verification failed:", error);
          toast({
            title: "Payment Verification Error",
            description: "Unable to verify payment status. Please refresh the page or contact support.",
            variant: "destructive",
          });
        }
      };
      
      verifyPayment();
    }
  }, [paymentStatus, user?.id, subscriptionData, refetchSubscription, toast]);

  // Check URL parameters for payment status
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get("status");
    if (status) {
      setPaymentStatus(status);
      // Clear URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  // Show payment status messages - only show confirmed statuses
  useEffect(() => {
    if (paymentStatus === "success") {
      // Don't immediately assume success - wait for backend verification
      // Check actual payment status from backend instead of trusting URL params
      toast({
        title: "Verifying Payment",
        description: "Please wait while we verify your payment status...",
      });
    } else if (paymentStatus === "cancelled") {
      toast({
        title: "Payment Cancelled",
        description: "Your payment was cancelled. You can try again anytime.",
        variant: "destructive",
      });
    } else if (paymentStatus === "failure") {
      toast({
        title: "Payment Failed",
        description: "Your payment failed. Please try again or contact support.",
        variant: "destructive",
      });
    }
  }, [paymentStatus, toast]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  if (isLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  const planLimits = {
    free: {
      credentials: 3,
      features: [
        "Basic resume builder",
        "3 credential uploads",
        "Public profile",
      ],
    },
    pro: {
      credentials: 20,
      features: [
        "Advanced resume builder",
        "20 credential uploads",
        "Analytics dashboard",
        "Priority support",
      ],
    },
    agency: {
      credentials: 100,
      features: [
        "Everything in Pro",
        "100 credential uploads",
        "Team management",
        "Custom branding",
      ],
    },
  };

  const currentPlan = user?.plan || "free";
  const isProOrAgency = currentPlan === "pro" || currentPlan === "agency";
  
  // Check if subscription is expiring soon or has expired
  const getSubscriptionStatus = () => {
    if (!subscriptionData?.subscription?.expiresAt || currentPlan === "free") {
      return { isExpiring: false, isExpired: false, daysUntilExpiry: null };
    }
    
    const expiryDate = new Date(subscriptionData.subscription.expiresAt);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    return {
      isExpiring: daysUntilExpiry <= 7 && daysUntilExpiry > 0,
      isExpired: daysUntilExpiry <= 0,
      daysUntilExpiry
    };
  };
  
  const subscriptionStatus = getSubscriptionStatus();

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50">
      <Navbar />
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Billing & Subscription
          </h1>
          <p className="text-gray-600">
            Manage your JuanCV subscription and billing
          </p>
        </div>

        {/* Subscription Status Warning */}
        {subscriptionStatus.isExpired && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-800">
              <Calendar className="w-5 h-5" />
              <h3 className="font-semibold">Subscription Expired</h3>
            </div>
            <p className="text-red-600 mt-1">
              Your subscription has expired. Please renew to continue using Pro features.
            </p>
          </div>
        )}
        
        {subscriptionStatus.isExpiring && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 text-yellow-800">
              <Calendar className="w-5 h-5" />
              <h3 className="font-semibold">Subscription Expiring Soon</h3>
            </div>
            <p className="text-yellow-600 mt-1">
              Your subscription expires in {subscriptionStatus.daysUntilExpiry} day{subscriptionStatus.daysUntilExpiry === 1 ? '' : 's'}. 
              Renew now to avoid losing access to Pro features.
            </p>
          </div>
        )}

        {/* Current Plan Status */}
        <Card className="mb-8 bg-white/70 backdrop-blur-sm border-amber-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="w-5 h-5 text-amber-600" />
              Current Plan
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Badge
                    variant={currentPlan === "free" ? "secondary" : "default"}
                    className={
                      currentPlan === "free"
                        ? "bg-gray-100"
                        : "bg-amber-100 text-amber-800"
                    }
                  >
                    {currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1)}{" "}
                    Plan
                  </Badge>
                  {currentPlan === "free" && (
                    <span className="text-sm text-gray-600">Free</span>
                  )}
                  {currentPlan === "pro" && (
                    <span className="text-sm text-green-600 font-medium">Active - ₱99/month</span>
                  )}
                </div>
                <p className="text-sm text-gray-600">
                  {
                    planLimits[currentPlan as keyof typeof planLimits]
                      .credentials
                  }{" "}
                  credential uploads included
                </p>
                {subscriptionData?.subscription?.expiresAt && currentPlan !== "free" && (
                  <p className="text-sm text-gray-600 mt-1">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    Expires: {new Date(subscriptionData.subscription.expiresAt).toLocaleDateString()}
                  </p>
                )}
              </div>
              {currentPlan === "free" && (
                <div className="text-right">
                  <p className="text-sm text-gray-600 mb-1">
                    Ready to upgrade?
                  </p>
                  <p className="text-xs text-gray-500">
                    Get more features and storage
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Plan Comparison */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {/* Free Plan */}
          <Card
            className={`relative ${currentPlan === "free" ? "ring-2 ring-amber-500" : ""} bg-white/70 backdrop-blur-sm border-amber-200`}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Free</span>
                {currentPlan === "free" && (
                  <Badge variant="secondary">Current</Badge>
                )}
              </CardTitle>
              <div className="text-2xl font-bold">
                ₱0<span className="text-sm font-normal">/month</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 mb-6">
                {planLimits.free.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-green-500" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
              {currentPlan === "free" && (
                <p className="text-xs text-gray-500 text-center">
                  You're currently on this plan
                </p>
              )}
            </CardContent>
          </Card>

          {/* Pro Plan */}
          <Card
            className={`relative ${currentPlan === "pro" ? "ring-2 ring-green-500 bg-green-50/70" : ""} bg-white/70 backdrop-blur-sm border-amber-200`}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Pro</span>
                {currentPlan === "pro" ? (
                  <Badge
                    variant="default"
                    className="bg-green-100 text-green-800"
                  >
                    Active
                  </Badge>
                ) : (
                  <Badge
                    variant="outline"
                    className="bg-amber-100 text-amber-800"
                  >
                    Popular
                  </Badge>
                )}
              </CardTitle>
              <div className="text-2xl font-bold">
                ₱99<span className="text-sm font-normal">/month</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 mb-6">
                {planLimits.pro.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-green-500" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
              {currentPlan === "free" && <UpgradeForm />}
              {currentPlan === "pro" && (
                <p className="text-xs text-green-600 text-center font-medium">
                  ✓ You're enjoying all Pro features
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Success Message */}
        {isProOrAgency && (
          <Card className="bg-green-50 border-green-200 mb-5">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <Check className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-green-800">
                    You're now Pro!
                  </h3>
                  <p className="text-sm text-green-700">
                    Welcome to JuanCV Pro! You now have access to all Pro features including 20 credential uploads, analytics dashboard, and priority support.
                  </p>
                  {subscriptionData?.subscription?.expiresAt && (
                    <p className="text-xs text-green-600 mt-1">
                      Your subscription is active until {new Date(subscriptionData.subscription.expiresAt).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Payment History */}
        <div className="grid grid-cols-1">
          <PaymentHistory userId={user.id} />
        </div>
      </div>
    </div>
  );
}
